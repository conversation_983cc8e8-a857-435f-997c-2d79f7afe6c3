﻿using MongoDB.Bson;
using MongoDB.Driver;

namespace Bigpara.Persistence.MongoDbContext;

public class MongoDbContext : IMongoDbContext
{
    private readonly IMongoDatabase _database;

    public MongoDbContext(IMongoDatabase database)
    {
        _database = database;
    }

    public IMongoCollection<T> GetCollection<T>(string collectionName) where T : class
    {
        return _database.GetCollection<T>(collectionName);
    }

    public async Task<T> GetByIdAsync<T>(string collectionName, ObjectId id) where T : class
    {
        var collection = GetCollection<T>(collectionName);
        var filter = Builders<T>.Filter.Eq("_id", id);
        return await collection.Find(filter).FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<T>> GetAllAsync<T>(
      string collectionName,
      string application = "com.bigpara") where T : class
    {
        var collection = GetCollection<T>(collectionName);

        var filter =

            Builders<T>.Filter.And(Builders<T>.Filter.Eq("ContentType", "Article"),
            Builders<T>.Filter.Eq("Application", application));
        var sort = Builders<T>.Sort.Descending("ModifiedDate");

        return await collection
            .Find(filter)
            .Sort(sort)
            .Limit(20)
            .ToListAsync();
    }

    public async Task<IEnumerable<T>> FindAsync<T>(string collectionName, FilterDefinition<T> filter) where T : class
    {
        var collection = GetCollection<T>(collectionName);
        return await collection.Find(filter).ToListAsync();
    }

    public async Task<IEnumerable<T>> FindAsync<T>(string collectionName) where T : class
    {
        var collection = GetCollection<T>(collectionName);
        return await collection.Find(_ => true).ToListAsync();
    }

    public async Task<T> FindOneAsync<T>(string collectionName, FilterDefinition<T> filter) where T : class
    {
        var collection = GetCollection<T>(collectionName);
        return await collection.Find(filter).FirstOrDefaultAsync();
    }

    public async Task InsertAsync<T>(string collectionName, T entity) where T : class
    {
        var collection = GetCollection<T>(collectionName);
        await collection.InsertOneAsync(entity);
    }

    public async Task InsertManyAsync<T>(string collectionName, IEnumerable<T> entities) where T : class
    {
        var collection = GetCollection<T>(collectionName);
        await collection.InsertManyAsync(entities);
    }

    public async Task<bool> UpdateAsync<T>(string collectionName, ObjectId id, T entity) where T : class
    {
        var collection = GetCollection<T>(collectionName);
        var filter = Builders<T>.Filter.Eq("_id", id);
        var result = await collection.ReplaceOneAsync(filter, entity);
        return result.ModifiedCount > 0;
    }

    public async Task<bool> DeleteAsync<T>(string collectionName, ObjectId id) where T : class
    {
        var collection = GetCollection<T>(collectionName);
        var filter = Builders<T>.Filter.Eq("_id", id);
        var result = await collection.DeleteOneAsync(filter);
        return result.DeletedCount > 0;
    }

    public async Task<long> DeleteManyAsync<T>(string collectionName, FilterDefinition<T> filter) where T : class
    {
        var collection = GetCollection<T>(collectionName);
        var result = await collection.DeleteManyAsync(filter);
        return result.DeletedCount;
    }

    public async Task<long> GetNextIncrementIdAsync(string collectionName, string application)
    {
        var collection = GetCollection<BsonDocument>("Numerators");

        var filter = Builders<BsonDocument>.Filter.And(
            Builders<BsonDocument>.Filter.Eq("Name", collectionName),
            Builders<BsonDocument>.Filter.Eq("Application", application)
        );

        var update = Builders<BsonDocument>.Update.Inc("Inc", 1);

        var options = new FindOneAndUpdateOptions<BsonDocument>
        {
            IsUpsert = true,
            ReturnDocument = ReturnDocument.After
        };

        var updatedDoc = await collection.FindOneAndUpdateAsync(filter, update, options);

        if (updatedDoc == null || !updatedDoc.Contains("Inc"))
        {
            throw new InvalidOperationException("Incremented document or 'Inc' field is missing.");
        }

        return updatedDoc["Inc"].ToInt64();
    }
}
