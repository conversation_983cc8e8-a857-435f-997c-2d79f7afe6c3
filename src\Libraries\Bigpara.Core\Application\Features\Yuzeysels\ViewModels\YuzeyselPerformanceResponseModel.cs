﻿using Bigpara.Core.Application.Common;

namespace Bigpara.Core.Application.Features.Yuzeysels.ViewModels
{
    public class YuzeyselPerformanceDataModel
    {
        public string Symbol { get; set; }

        // Alış fiyatı
        public decimal Bid { get; set; }

        // Satış fiyatı
        public decimal Ask { get; set; }

        // Günlük kapanış fiyatı
        public decimal DayClose { get; set; }

        // Günlük değişim yüzdesi
        public decimal DayChangePercent { get; set; }

        // Haftalık kapanış fiyatı
        public decimal WeekClose { get; set; }

        // Aylık kapanış fiyatı
        public decimal MonthClose { get; set; }

        // Yıllık kapanış fiyatı
        public decimal YearClose { get; set; }

        // Haftalık değişim yüzdesi
        public decimal WeekChangePercent { get; set; }

        // Aylık değişim yüzdesi
        public decimal MonthChangePercent { get; set; }

        // Yıllık değişim yüzdesi
        public decimal YearChangePercent { get; set; }
    }
    public class YuzeyselPerformanceResponseModel : BaseResponse
    {
        public List<YuzeyselPerformanceDataModel> Data { get; set; }
    }
}
