﻿using Bigpara.Core.Application.Features.Symbols.Commands;
using Bigpara.Core.Application.Features.Symbols.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace Bigpara.Service.Api.Controllers
{
    [Authorize]
    [ApiController]
    public class FollowController : ControllerBase
    {
        private readonly IMediator _mediator;
        public FollowController(IMediator mediator)
        {
            _mediator = mediator;
        }
        [HttpPost]
        [Route("api/follow/operation")]
        public async Task<IActionResult> AddOrRemoveFollow([FromBody] AddOrRemoveFollowSymbolCommand command)
        {
            await _mediator.Send(command);

            return Ok();
        }

        [HttpGet]
        [Route("api/follow/list")]
        public async Task<IActionResult> List([FromQuery]GetListFollowSymbolsQuery request)
        {
            var result = await _mediator.Send(request);
            return Ok(result);
        }
    }
}
