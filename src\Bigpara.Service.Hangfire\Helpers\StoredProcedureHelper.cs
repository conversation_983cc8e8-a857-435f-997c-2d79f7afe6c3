﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Bigpara.Service.Hangfire.Infrastructre.Data;
using Microsoft.Data.SqlClient;

namespace Bigpara.Service.Hangfire.Helpers;

public class StoredProcedureHelper
{
    private readonly IStoredProcedureParameterService _storedProcedureService;

    public StoredProcedureHelper(IStoredProcedureParameterService storedProcedureService)
    {
        _storedProcedureService = storedProcedureService;
    }

    public SqlParameter[]? CheckHisseAcilisParameter(YuzeyselDto yuzeyselDto)
    {
        if (yuzeyselDto.ACILIS == null)
        {
            return null;
        }

        var hisseAcilisParameters = _storedProcedureService.CreateHisseAcilisStoredProcedureParameters(yuzeyselDto);

        return hisseAcilisParameters;
    }

    public SqlParameter[]? CheckHisseTarihselParameter(YuzeyselDto yuzeyselDto)
    {
        if (yuzeyselDto.HAFTADUSUK == null || yuzeyselDto.HAFTAYUKSEK == null || yuzeyselDto.AYDUSUK == null || yuzeyselDto.AYYUKSEK == null || yuzeyselDto.YILDUSUK == null || yuzeyselDto.YILYUKSEK == null)
        {
            return null;
        }

        var hisseHistoricalParameters = _storedProcedureService.CreateHisseHistoricalStoredProcedureParameters(yuzeyselDto);

        return hisseHistoricalParameters;
    }

    public SqlParameter[]? CheckHisseIstatistikRealTimeParameter(YuzeyselDto yuzeyselDto)
    {
        if (!yuzeyselDto.ALIS.HasValue || !yuzeyselDto.SATIS.HasValue)
        {
            return null;
        }

        var hisseHisseIstatistikRealTimeParameters = _storedProcedureService.CreateHisseIstatistikRealTimeStoredProcedureParameters(yuzeyselDto);

        return hisseHisseIstatistikRealTimeParameters;
    }

    public SqlParameter[]? CheckHisseIstatistikParameter(YuzeyselDto yuzeyselDto)
    {
        if (!yuzeyselDto.ALIS.HasValue || !yuzeyselDto.SATIS.HasValue)
        {
            return null;
        }

        var hisseHisseIstatistikParameters = _storedProcedureService.CreateHisseIstatistikStoredProcedureParameters(yuzeyselDto);

        return hisseHisseIstatistikParameters;
    }

    public SqlParameter[]? CheckHisseKapanisParameter(YuzeyselDto yuzeyselDto)
    {
        if (yuzeyselDto.KAPANIS == null)
        {
            return null;
        }

        var hisseKapanisParameters = _storedProcedureService.CreateHisseKapanisStoredProcedureParameters(yuzeyselDto);

        return hisseKapanisParameters;
    }

    public SqlParameter[]? CheckHisseMarjParameter(YuzeyselDto yuzeyselDto, SymbolsCacheDto symbols)
    {

        yuzeyselDto.FIYATADIMI = PriceStepCalculatorHelper.GetPriceStep(symbols.Sembol.PttRow, yuzeyselDto.ALIS.GetValueOrDefault(0));
        yuzeyselDto.HisseGrubu = symbols.Sembol.ImkbPazarKod;

        if (!yuzeyselDto.TABAN.HasValue || !yuzeyselDto.TAVAN.HasValue)
        {
            return null;
        }

        var hisseMarjParameters = _storedProcedureService.CreateHisseMarjStoredProcedureParameters(yuzeyselDto);

        return hisseMarjParameters;
    }

    public SqlParameter[]? CheckHisseOrtalamaRealTimeParameter(YuzeyselDto yuzeyselDto)
    {
        if (!yuzeyselDto.AORT.HasValue)
        {
            return null;
        }

        var hisseOrtalamaRealTimeParameters = _storedProcedureService.CreateHisseOrtalamaRealTimeStoredProcedureParameters(yuzeyselDto);

        return hisseOrtalamaRealTimeParameters;
    }

    public SqlParameter[]? CheckHisseOrtalamaParameter(YuzeyselDto yuzeyselDto)
    {
        if (!yuzeyselDto.AORT.HasValue)
        {
            return null;
        }

        var hisseOrtalamaParameters = _storedProcedureService.CreateHisseOrtalamaStoredProcedureParameters(yuzeyselDto);

        return hisseOrtalamaParameters;
    }

    public SqlParameter[]? CheckHisseSonRealTimeParameter(YuzeyselDto yuzeyselDto)
    {
        if (!yuzeyselDto.KAPANIS.HasValue)
        {
            return null;
        }

        var hisseSonRealTimeParameters = _storedProcedureService.CreateHisseSonRealTimeStoredProcedureParameters(yuzeyselDto);

        return hisseSonRealTimeParameters;
    }

    public SqlParameter[]? CheckHisseSonParameter(YuzeyselDto yuzeyselDto)
    {
        if (!yuzeyselDto.KAPANIS.HasValue)
        {
            return null;
        }

        var hisseSonParameters = _storedProcedureService.CreateHisseSonStoredProcedureParameters(yuzeyselDto);

        return hisseSonParameters;
    }

    public SqlParameter[]? CheckHisseToplamParameter(YuzeyselDto yuzeyselDto)
    {
        if (!yuzeyselDto.HACIMLOT.HasValue)
        {
            return null;
        }

        var hisseToplamParameters = _storedProcedureService.CreateHisseToplamStoredProcedureParameters(yuzeyselDto);

        return hisseToplamParameters;
    }

    public SqlParameter[]? CheckImkbVipSessionParameter(YuzeyselDto yuzeyselDto)
    {
        if (yuzeyselDto.KAPANIS == null)
        {
            return null;
        }

        var imkbVipSessionParameters = _storedProcedureService.CreateImkbVipSessionStoredProcedureParameters(yuzeyselDto);

        return imkbVipSessionParameters;
    }

    public SqlParameter[]? CheckImkbVipSessionHD1PParameter(YuzeyselDto yuzeyselDto)
    {
        if (yuzeyselDto.HACIMLOT == null)
        {
            return null;
        }

        var imkbVipSessionHD1PParameters = _storedProcedureService.CreateImkbVipSessionHD1PStoredProcedureParameters(yuzeyselDto);

        return imkbVipSessionHD1PParameters;
    }

    public SqlParameter[]? CheckImkbViopHistoricalParameter(YuzeyselDto yuzeyselDto)
    {
        var imkbViopHistoricalStoredProcedureParameters = _storedProcedureService.CreateImkbViopHistoricalStoredProcedureParameters(yuzeyselDto);

        return imkbViopHistoricalStoredProcedureParameters;
    }

    public SqlParameter[]? CheckYuzeyselFidesPariteParameter(YuzeyselDto yuzeyselDto)
    {
        if (!yuzeyselDto.ALIS.HasValue || !yuzeyselDto.SATIS.HasValue)
        {
            return null;
        }

        var fidesPariteStoredProcedureParameters = _storedProcedureService.CreateFidesPariteStoredProcedureParameters(yuzeyselDto);

        return fidesPariteStoredProcedureParameters;
    }

    public SqlParameter[]? CheckYuzeyselFidesPariteRealTimeParameter(YuzeyselDto yuzeyselDto)
    {
        if (!yuzeyselDto.ALIS.HasValue || !yuzeyselDto.SATIS.HasValue)
        {
            return null;
        }

        var fidesPariteRealTimeStoredProcedureParameters = _storedProcedureService.CreateFidesPariteRealTimeStoredProcedureParameters(yuzeyselDto);

        return fidesPariteRealTimeStoredProcedureParameters;
    }

    public SqlParameter[]? CheckYuzeyselFidesPariteHistoricalParameter(YuzeyselDto yuzeyselDto)
    {
        if (!yuzeyselDto.ALIS.HasValue || !yuzeyselDto.SATIS.HasValue)
        {
            return null;
        }

        var fidesPariteHistoricalStoredProcedureParameters = _storedProcedureService.CreateFidesPariteHistoricalStoredProcedureParameters(yuzeyselDto);

        return fidesPariteHistoricalStoredProcedureParameters;
    }


    public SqlParameter[]? CheckImkbEndeksParameter(YuzeyselDto yuzeyselDto)
    {

        return _storedProcedureService.CreateImkbEndeksStoredProcedureParameters(yuzeyselDto);
    }

    public SqlParameter[]? CheckImkbEndeksToplamParameter(YuzeyselDto yuzeyselDto)
    {
        if (yuzeyselDto.HACIMTL == null || yuzeyselDto.HACIMLOT == null)
        {
            return null;
        }

        return _storedProcedureService.CreateImkbEndeksToplamStoredProcedureParameters(yuzeyselDto);
    }

    public SqlParameter[]? CheckImkbEndeksRealTimeParameter(YuzeyselDto yuzeyselDto)
    {
        return _storedProcedureService.CreateImkbEndeksRealTimeStoredProcedureParameters(yuzeyselDto);
    }

    public SqlParameter[]? CheckFidesGelecekParameter(YuzeyselDto yuzeyselDto)
    {
        var fidesGelecekParameters = _storedProcedureService.CreateFidesGelecekStoredProcedureParameters(yuzeyselDto);

        return fidesGelecekParameters;
    }

    public SqlParameter[]? CheckFidesEndeksParameter(YuzeyselDto yuzeyselDto)
    {
        var fidesEndeksParameters = _storedProcedureService.CreateFidesEndeksStoredProcedureParameters(yuzeyselDto);

        return fidesEndeksParameters;
    }

    public SqlParameter[]? CheckBolunmeParameter(BolunmeDto bolunmeDto)
    {
        var bolunmeParameters = _storedProcedureService.CreateBolunmeStoredProcedureParameters(bolunmeDto);

        return bolunmeParameters;
    }
}
