﻿namespace Bigpara.Domain.Matriks;

public partial class Indikatorler
{
    public string SEMBOL { get; set; }
    public System.DateTime TARIH { get; set; }
    public Nullable<System.DateTime> UpdatedDateTime { get; set; }
    public Nullable<double> MOV_5 { get; set; }
    public Nullable<double> MOV_9 { get; set; }
    public Nullable<double> MOV_10 { get; set; }
    public Nullable<double> MOV_20 { get; set; }
    public Nullable<double> MOV_50 { get; set; }
    public Nullable<double> MOV_100 { get; set; }
    public Nullable<double> MOV_250 { get; set; }
    public Nullable<double> BBW_20_2_Up { get; set; }
    public Nullable<double> BBW_20_2_Middle { get; set; }
    public Nullable<double> BBW_20_2_Down { get; set; }
    public Nullable<double> CCI_10 { get; set; }
    public Nullable<double> CCI_14 { get; set; }
    public Nullable<double> CCI_20 { get; set; }
    public Nullable<double> MACD_26_12 { get; set; }
    public Nullable<double> MACD_Trigger_9 { get; set; }
    public Nullable<double> Momentum_12 { get; set; }
    public Nullable<double> SAR { get; set; }
    public Nullable<double> RSI_9 { get; set; }
    public Nullable<double> RSI_14 { get; set; }
    public Nullable<double> FStoch_K_5 { get; set; }
    public Nullable<double> FStoch_D_3 { get; set; }
    public Nullable<double> SStoch_K_5_5 { get; set; }
    public Nullable<double> SStoch_D_3 { get; set; }
    public Nullable<double> STC_5_1 { get; set; }
    public Nullable<double> STC_5_3 { get; set; }
    public Nullable<double> STC_13_1 { get; set; }
    public Nullable<double> STC_21_1 { get; set; }
    public Nullable<double> ADX_3 { get; set; }
    public Nullable<double> MOV_CCI_10_5 { get; set; }
    public Nullable<double> MOV_CCI_10_10 { get; set; }
    public Nullable<double> MOV_MACD_5_E { get; set; }
    public string STC_5_3_T { get; set; }
    public string MACD_STR { get; set; }
    public string CCI_20_STR { get; set; }
}
