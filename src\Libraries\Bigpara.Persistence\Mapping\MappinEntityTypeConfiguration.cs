﻿using Bigpara.Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bigpara.Persistence.Mapping;

public class MappinEntityTypeConfiguration<TEnity> : IMappingConfiguration, IEntityTypeConfiguration<TEnity> where TEnity : BaseEntity
{
    public virtual void ApplyConfiguration(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(this);

    }

    public virtual void Configure(EntityTypeBuilder<TEnity> builder)
    {
      
    }
}
