﻿namespace Bigpara.Notifications;

public enum NotificationChannel
{
    /// <summary>
    /// Bildirim sadece e-posta yoluyla gönderilir.
    /// </summary>
    Email,

    /// <summary>
    /// Bildirim sadece Microsoft Teams üzerinden gönderilir.
    /// </summary>
    Teams,

    /// <summary>
    /// Hem e-posta hem de Microsoft Teams üzerinden bildirim gönderilir.
    /// </summary>
    /// <remarks>
    /// Bu se<PERSON><PERSON>, birden fazla kanal aracılığıyla bilgilendirme yapılmasını sağlar.
    /// </remarks>
    Both

}
