using System;

namespace Bigpara.Domain.Bigpara;

public partial class News
{
    public int Id { get; set; }
    public string Title { get; set; }
    public string SeoTitle { get; set; }
    public string SpotTitle { get; set; }
    public string SeoKeywords { get; set; }
    public string SeoDesc { get; set; }
    public string SiteUrl { get; set; }
    public string Body { get; set; }
    public string Picture { get; set; }
    public int NewsCategoryId { get; set; }
    public int SourceId { get; set; }
    public bool IsActive { get; set; }
    public bool IsPhotoNew { get; set; }
    public bool IsAdvertNew { get; set; }
    public bool ShareWithHurriyetSocial { get; set; }
    public string AdvertNewUrl { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime? CreatedDate { get; set; }
    public DateTime? UpdatedDate { get; set; }
    public int ReadCount { get; set; }
    public int NewsOrder { get; set; }
    public int HurbrandTypeID { get; set; }
    public bool HasSubCategatories { get; set; }
    public string HeadlineLink { get; set; }
    public bool IsGoogleValidContent { get; set; }
    public bool IsSocialPosted { get; set; }
    public bool SentToSocial { get; set; }
    public bool IsApplicationNews { get; set; }
    public bool IsMobilSiteNews { get; set; }
    public bool IsWebSiteNews { get; set; }
    public bool IsShowHurriyet { get; set; }

    //Relation Fields
    public string CategoryName { get; set; }



    public override bool Equals(object obj)
    {
        if (obj == null) return false;
        News objAsPart = obj as News;
        if (objAsPart == null) return false;
        else return Equals(objAsPart);
    }
    public override int GetHashCode()
    {
        return Id;
    }
    public bool Equals(News other)
    {
        if (other == null) return false;
        return Id.Equals(other.Id);
    }

    public string Type { get; set; }

    public int DailyReadCount { get; set; }
    public int WeeklyReadCount { get; set; }
    public int TotalReadCount { get; set; }
}

