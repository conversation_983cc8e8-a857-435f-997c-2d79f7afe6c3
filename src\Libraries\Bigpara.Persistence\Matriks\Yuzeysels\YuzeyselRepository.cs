﻿using Bigpara.Core.Application.Contracts.Yuzeysels;
using Bigpara.Domain.Matriks;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace Bigpara.Persistence.Matriks.Yuzeysels;

public class YuzeyselRepository : IYuzeyselRepository
{
    private readonly IMatriksDbContext _matriksDbContext;
    public YuzeyselRepository(IMatriksDbContext matriksDbContext)
    {
        _matriksDbContext = matriksDbContext;
    }

    public async Task<Yuzeysel> GetById(int sembolId)
    {
        var parameters = new List<SqlParameter>() ;
        parameters.Add(new SqlParameter("sembolId", sembolId));

        return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<Yuzeysel>("bp.pGetYuzeyselBySembolId", parameters.ToArray());   

    }

    public async Task<Yuzeysel> GetBySembol(string sembol)
    {
        var parameters = new List<SqlParameter>();
        parameters.Add(new SqlParameter("sembol", sembol));

        return await  _matriksDbContext.ExecuteStoredProcedureSingleAsync<Yuzeysel>("bp.pGetYuzeyselBySembol", parameters.ToArray());
     
    }

    public async Task<List<Yuzeysel>> GetBySembols(string sembols)
    {
        var parameters = new List<SqlParameter>();
        parameters.Add(new SqlParameter("sembol", sembols));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<Yuzeysel>("bp.pGetYuzeyselBySembols", parameters.ToArray());

    }

    public async Task<List<Yuzeysel>> GetPerformanceBySembols(string symbols)
    {
        List<string> symbolList = symbols.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
        return  await _matriksDbContext.Yuzeysels.Where(p=> EF.Constant( symbolList).Contains(p.SEMBOL))
            .ToListAsync();
    }

    public async Task<Yuzeysel> GetYuzeyselBySembolTarih(string sembolId, DateTime tarih)
    {
        var parameters = new List<SqlParameter>();
        parameters.Add(new SqlParameter("sembol", sembolId));
        parameters.Add(new SqlParameter("tarih", tarih));

        return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<Yuzeysel>("bp.pGetYuzeyselBySembolTarih", parameters.ToArray()); 
    
    }

    public async Task< Yuzeysel> gunlukOzet()
    {
        return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<Yuzeysel>("bp.pGetYuzeyselBistOzet");

    }
}
