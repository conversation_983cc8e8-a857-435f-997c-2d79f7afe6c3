﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Core.Application.Contracts.Yuzeysels;

public interface IYuzeyselRepository
{
    Task<Yuzeysel> GetById(int sembolId);
    Task<Yuzeysel> GetBySembol(string sembol);
    Task<Yuzeysel> gunlukOzet();
    Task<Yuzeysel> GetYuzeyselBySembolTarih(string sembolId, DateTime tarih);
    Task<List<Yuzeysel>> GetBySembols(string sembols);
    Task<List<Yuzeysel>> GetPerformanceBySembols(string sembols);
}
