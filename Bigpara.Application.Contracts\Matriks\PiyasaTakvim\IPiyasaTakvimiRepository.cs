﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Core.Application.Contracts.Matriks.PiyasaTakvim;

public interface IPiyasaTakvimiRepository
{
    Task<List<PiyasaTakvimi>> GetPiyasaTakvimiTopData(int topCount);
    Task<List<PiyasaTakvimi>> GetPiyasaTakvimiList(DateTime baslang<PERSON><PERSON><PERSON>, int gunSayisi, short tarihSayisi);
    Task<bool> InsertOrUpdatePiyasaTakvimi(PiyasaTakvimi piyasaTakvimi);
    Task<int> GetTotalPiyasaTakvimiCount();
    Task<List<PiyasaTakvimi>> GetPiyasaTakvimiByPaging(int intervalStart, int intervalEnd);
    Task<PiyasaTakvimi> GetPiyasaTakvimiById(int id);
    Task DeletePiyasaTakvimi(int Id);
}