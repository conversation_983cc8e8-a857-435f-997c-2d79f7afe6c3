﻿using System.Net.Mail;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;

namespace Bigpara.Notifications.Email;

public class EmailService : IEmailService
{
    private readonly EmailConfiguration _emailConfig;
    private readonly IConfiguration _configuration;

    public EmailService(IOptions<EmailConfiguration> emailConfig, IConfiguration configuration)
    {
        _configuration = configuration;
        var emailSettings = _configuration.GetSection("EmailSettings");
        _emailConfig = emailConfig.Value;

    }

    public async Task SendErrorEmailAsync(string jobName, string errorMessage)
    {
        //await SendEmailAsync($"Environment: {_emailConfig.Environment}, 🚨 {jobName} HATASI TESPİT EDİLDİ!", $"<p>{errorMessage}</p>", _emailConfig.DefaultRecipient);
    }

    public async Task SendInfoEmailAsync(string jobName, string infoMessage)
    {
       //await SendEmailAsync($"Environment: {_emailConfig.Environment}, 🔔 {jobName} Başladı!", $"<p>{infoMessage}</p>", _emailConfig.DefaultRecipient);
    }

    private async Task SendEmailAsync(string subject, string body, List<string> recipient)
    {
        using var client = new SmtpClient(_emailConfig.SmtpServer, _emailConfig.Port)
        {
            Credentials = new System.Net.NetworkCredential(_emailConfig.SenderEmail, _emailConfig.SenderPassword),
            EnableSsl = true
        };


        var mailMessage = new MailMessage
        {
            From = new MailAddress(_emailConfig.SenderEmail),
            Subject = subject,
            Body = body,
            IsBodyHtml = true
        };

        foreach (var recipientEmail in recipient)
        {
            mailMessage.To.Add(recipientEmail);
        }

        await client.SendMailAsync(mailMessage);
    }
}