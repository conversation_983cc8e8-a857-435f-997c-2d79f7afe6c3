using System.Threading;
using System.Threading.Tasks;
using Bigpara.Core.Application.Features.Symbols.Queries;
using Bigpara.Core.Application.Features.Symbols.Queries.ViewModels;
using Bigpara.Service.Api.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Bigpara.Service.Api.Tests.Controllers
{
    public class SymbolControllerTests
    {
        private readonly Mock<ILogger<NewsController>> _loggerMock;
        private readonly Mock<IMediator> _mediatorMock;
        private readonly SymbolController _controller;

        public SymbolControllerTests()
        {
            _loggerMock = new Mock<ILogger<NewsController>>();
            _mediatorMock = new Mock<IMediator>();
            _controller = new SymbolController(_loggerMock.Object, _mediatorMock.Object);
        }

        [Fact]
        public async Task Search_ReturnsOkResult_WithExpectedData()
        {
            // Arrange
            var query = new GetSymbolSearchQuery { Type = "test", Keyword = "abc", PageIndex = 1, PageSize = 10, BaazarId = 1 };
            var expectedResult = new SymbolSearchQueryViewModel();
            _mediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.Search(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expectedResult, okResult.Value);
        }

        [Fact]
        public async Task Trends_ReturnsOkResult_WithExpectedData()
        {
            // Arrange
            var query = new GetTrendsSymbolQuery { Type = "trend", Size = 5 };
            var expectedResult = new SymbolSearchQueryViewModel();
            _mediatorMock.Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.Trends(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(expectedResult, okResult.Value);
        }
    }
}
