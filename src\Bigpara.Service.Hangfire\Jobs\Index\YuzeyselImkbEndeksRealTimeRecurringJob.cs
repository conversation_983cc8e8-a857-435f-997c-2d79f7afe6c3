﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructre.HttpClients;
using Bigpara.Service.Hangfire.Services.Interfaces;
using Bigpara.Service.Hangfire.Jobs.Stocks;

namespace Bigpara.Service.Hangfire.Jobs.Index;

public class YuzeyselImkbEndeksRealTimeRecurringJob : IRecurringJob
{
    private readonly ForeksRealTimeHttpClient _foreksRealTimeHttpClient;
    private readonly ILogger<YuzeyselImkbHisseRecurringJob> _logger;
    private readonly IConfiguration _configuration;
    private readonly IEndeksService _endeksService;
    private readonly INotificationService _notificationService;
    private readonly int _maxConcurrentTasks;

    public YuzeyselImkbEndeksRealTimeRecurringJob(
        ForeksRealTimeHttpClient foreksRealTimeHttpClient,
        ILogger<YuzeyselImkbHisseRecurringJob> logger,
        IConfiguration configuration,
        IEndeksService endeksService,
        INotificationService notificationService)
    {
        _foreksRealTimeHttpClient = foreksRealTimeHttpClient;
        _logger = logger;
        _configuration = configuration;
        _endeksService = endeksService;
        _notificationService = notificationService;
        _maxConcurrentTasks = _configuration.GetValue("YuzeyselImkbEndeksRealTimeProcessing:MaxConcurrentTasks", 10);
    }

    public string Name => "YuzeyselImkbEndeksRealTimeRecurringJob";
    public IEnumerable<string> Crons => ["0 0/2 * * * ?"];

    public async Task ExecuteAsync()
    {
        try
        {
            await ProcessDataTypeAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"{Name} sırasında kritik hata oluştu.");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex}, Hata Mesajı: {ex.Message}");
        }
    }

    public async Task ProcessDataTypeAsync()
    {
        try
        {
            var url = $"{_configuration["Foreks:SnapShotApi"]}/?{_configuration["Foreks:SnapShot:Endeks:Url"]}";
            if (string.IsNullOrEmpty(url))
            {
                _logger.LogError("Foreks:SnapShot:Endeks:Url değeri bulunamadı.");
                await _notificationService.NotifyErrorAsync(Name, "Foreks:SnapShot:Endeks:Url değeri bulunamadı.");
                return;
            }

            var result = await _foreksRealTimeHttpClient.FetchDataAsync<YuzeyselDto>(url);
            if (result == null || result.Count == 0)
            {
                _logger.LogWarning("Çekilen veri boş veya null.");
                return;
            }

            using var semaphore = new SemaphoreSlim(_maxConcurrentTasks);

            foreach (var item in result)
            {
                await semaphore.WaitAsync();

                try
                {
                    await ProcessSingleItemAsync(item);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"İşlem sırasında hata oluştu: {item.SEMBOL}");
                    await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.SEMBOL} , Hata: {ex} , Hata Mesajı: {ex.Message}");
                }
                finally
                {
                    semaphore.Release();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, ex.Message);
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {ex.Message}, Hata Mesajı: {ex.Message}");
        }
    }

    private async Task ProcessSingleItemAsync(YuzeyselDto item)
    {
        try
        {
            await _endeksService.ChangeRealTime(item);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {item.SEMBOL}");
            await _notificationService.NotifyErrorAsync(Name, $"İşlem sırasında hata oluştu: {item.SEMBOL} , Hata: {ex} , Hata Mesajı: {ex.Message}");
        }
    }
}
