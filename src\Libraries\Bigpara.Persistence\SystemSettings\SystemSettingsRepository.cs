﻿using Bigpara.Core.Application.Contracts.SystemSettings;
using Bigpara.Domain.Bigpara;
using Bigpara.Domain.SystemSettings;
using Microsoft.Data.SqlClient;

namespace Bigpara.Persistence.SystemSettings;

public class SystemSettingsRepository : ISystemSettingsRepository
{
    private readonly IBigparaDbContext _bigparaDbContext;

    public SystemSettingsRepository(IBigparaDbContext bigparaDbContext)
    {
        _bigparaDbContext = bigparaDbContext;
    }

    public async Task<SystemParameterData> GetSystemSettingsValueAsync(string owner, string key)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("owner", owner),
            new SqlParameter("configKey", key)
        };

        var result = await _bigparaDbContext.ExecuteQuerySingleAsync<SystemConfiguration>("spGetSystemParameter", parameters.ToArray());

        if (result == null)
            return null;

        return new SystemParameterData
        {
            Value = result.ConfigValue,
            Type = result.ParameterType
        };
    }

    public async Task<List<SystemParameterData>> GetAllSystemSettingsAsync()
    {
        var list = new List<SystemParameterData>();
        var results = await _bigparaDbContext.ExecuteStoredProcedureAsync<SystemConfiguration>("spGetAllSystemParameter");

        foreach (var config in results)
        {
            list.Add(new SystemParameterData
            {
                Value = config.ConfigValue,
                Type = config.ParameterType,
                ConfigKey = config.ConfigKey,
                Owner = config.Owner
            });
        }

        return list;
    }
}
