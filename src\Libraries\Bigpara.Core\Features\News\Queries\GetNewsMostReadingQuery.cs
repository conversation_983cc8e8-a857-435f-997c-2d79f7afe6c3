﻿using Bigpara.Application.Features.News.Queries.ViewModels;
using Bigpara.Core.Application.Contracts.Cms;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Application.Features.News.Queries;

#region Query
public class GetNewsMostReadingQuery : IRequest<NewsMostReadingViewModel>
{
    public int Size { get; set; }

}
#endregion

#region Handler
public class GetNewMostReadQueryHandler : IRequestHandler<GetNewsMostReadingQuery, NewsMostReadingViewModel>
{
    private readonly INewsSearchRepository _searchRepository;

    public GetNewMostReadQueryHandler(INewsSearchRepository searchRepository)
    {
        _searchRepository = searchRepository;
    }

    public async Task<NewsMostReadingViewModel> Handle(GetNewsMostReadingQuery request, CancellationToken cancellationToken)
    {

        var news = await _searchRepository.Search(null, "Stats.Hit", -1, 0, 10);

        NewsMostReadingViewModel response = new NewsMostReadingViewModel();
        response.Data = new
        {
            news = news.Data,
            pageIndex = news.PageIndex,
            pageSize = news.PageSize
        };

        return response;

    }
}

#endregion

