﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Serilog.Events;
using Serilog.Sinks.Elasticsearch;
using Serilog;
using Serilog.Exceptions;
using Microsoft.AspNetCore.Builder;
using System.Net;
using Microsoft.AspNetCore.Diagnostics;

namespace Bigpara.Logging.Extensions;

public static class ServiceCollectionExtensions
{
    public static void UseCustomLogging(this IServiceCollection services, IConfiguration configuration)
    {
        try
        {
            Log.Logger = new LoggerConfiguration()
                .Enrich.FromLogContext()
                .Enrich.WithExceptionDetails()
                .Enrich.WithMachineName()
                .Enrich.WithProperty("Application", configuration.GetValue<string>("SeriLog:Platform"))
                .Enrich.WithProperty("Env", configuration.GetValue<string>("SeriLog:Env"))
                .MinimumLevel.Error()
                .MinimumLevel.Override("Microsoft", LogEventLevel.Error)
                .MinimumLevel.Override("System", LogEventLevel.Error)
                .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri(configuration.GetValue<string>("SeriLog:Url")))
                {
                    AutoRegisterTemplate = true,
                    DetectElasticsearchVersion = true,
                    AutoRegisterTemplateVersion = AutoRegisterTemplateVersion.ESv7,
                    IndexFormat = "logstash-application-{0:yyyy.MM.dd}",
                    RegisterTemplateFailure = RegisterTemplateRecovery.FailSink,
                    EmitEventFailure = EmitEventFailureHandling.WriteToSelfLog |
                                       EmitEventFailureHandling.WriteToFailureSink |
                                       EmitEventFailureHandling.RaiseCallback
                })
                .CreateLogger();

            services.AddLogging(builder =>
            {
                builder.AddSerilog(Log.Logger, dispose: true);
            });

            Log.Information("Bigpara Cloud is running...");
        }
        catch (Exception ex)
        {
            Console.WriteLine("AddSerilog Error: " + ex.Message);
        }
    }

    public static void UseGlobalExceptionLogging(this IApplicationBuilder app)
    {
        app.UseExceptionHandler(errorApp =>
        {
            errorApp.Run(async context =>
            {
                context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                context.Response.ContentType = "application/json";

                IExceptionHandlerFeature error = context.Features.Get<IExceptionHandlerFeature>();

                if (error != null)
                {
                    Log.Error(error.Error, "Global Error Handling");
                }
            });
        });
    }
}
