﻿namespace Bigpara.Configuration.AdoNet;

public interface IBigparaCmsConfiguration
{

    /// <summary>
    /// Gets the Bigpara CMS version.
    /// </summary>
    /// <value>
    /// The Bigpara CMS version.
    /// </value>
    string Version { get; }

    /// <summary>
    /// Gets a value indicating whether CMS should use minified resources (*.min.js and *.min.css).
    /// </summary>
    /// <value>
    ///   <c>true</c> if CMS should use minified resources; otherwise, <c>false</c>.
    /// </value>
    bool UseMinifiedResources { get; }

    /// <summary>
    /// Gets the CMS resources (*.js and *.css) base path.
    /// </summary>
    /// <value>
    /// The CMS content base path.
    /// </value>
    string ResourcesBasePath { get; }

    /// <summary>
    /// Gets or sets the login URL.
    /// </summary>
    /// <value>
    /// The login URL.
    /// </value>
    string LoginUrl { get; set; }

    /// <summary>
    /// Gets or sets the web site URL.
    /// </summary>
    /// <value>
    /// The web site URL.
    /// </value>
    string WebSiteUrl { get; set; }

    /// <summary>
    /// Gets the virtual root path (like "~/App_Data") of Bigpar working directory. 
    /// </summary>
    /// <value>
    /// The virtual root path of Bigpara working directory.
    /// </value>
    string WorkingDirectoryRootPath { get; }


    /// <summary>
    /// Gets or sets the page not found url.
    /// </summary>
    /// <value>
    /// The page not found url.
    /// </value>
    string PageNotFoundUrl { get; set; }


}
