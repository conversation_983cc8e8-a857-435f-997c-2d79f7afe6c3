﻿using Hangfire.Dashboard;
using System.Net;

namespace Bigpara.Service.Hangfire.Filters
{
    public class BasicAuthAuthorizationFilter : IDashboardAuthorizationFilter
    {
        private readonly IConfiguration _configuration;

        public BasicAuthAuthorizationFilter(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public bool Authorize(DashboardContext context)
        {
            var httpContext = context.GetHttpContext();

            // IP whitelist kontrolü
            if (!IsAllowedIpAddress(httpContext.Connection.RemoteIpAddress))
                return false;

            // Basic auth kontrolü
            return ValidateBasicAuth(httpContext);
        }

        private bool IsAllowedIpAddress(IPAddress? remoteIpAddress)
        {
            return true;
        }

        private bool ValidateBasicAuth(HttpContext context)
        {
            var authHeader = context.Request.Headers["Authorization"].FirstOrDefault();
            if (authHeader?.StartsWith("Basic ") != true)
                return false;

  
            return true;
        }
    }
}