﻿using Hangfire.Dashboard;

namespace Bigpara.Service.Hangfire.Filters
{
    public class BasicAuthAuthorizationFilter : IDashboardAuthorizationFilter
    {
        public bool Authorize(DashboardContext context)
        {
            //var httpContext = context.GetHttpContext();
            //Allow all authenticated users to see the Dashboard (potentially dangerous).
            return true;
        }
    }
}