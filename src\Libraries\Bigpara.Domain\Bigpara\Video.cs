﻿namespace Bigpara.Domain.Bigpara;

public partial class Video 
{
    public int Id { get; set; }
    public DocumentTypes DocumentTypes { get; set; }
    public string PrerollAd { get; set; }
    public int RowIdx { get; set; }
    public Nullable<System.DateTime> CreateDate { get; set; }
    public Nullable<byte> Status { get; set; }
    public Nullable<bool> IsOutVideo { get; set; }
    public string IFrameCode { get; set; }
    public string Title { get; set; }
    public Nullable<int> VideoID { get; set; }
    public Nullable<int> DomainID { get; set; }
    public string ThumbnailUrl { get; set; }
    public string VideoUrl { get; set; }
    public string WebUrl { get; set; }
    public string PlaylistWebUrl { get; set; }
    public string Explanation { get; set; }

    public string Tags { get; set; }

    //public List<string> Tags { get; set; }
    public Nullable<int> CategoryId { get; set; }
    public string CategoryName { get; set; }
    public string CategoryColor { get; set; }
    public Nullable<int> CustomListId { get; set; }
    public Nullable<bool> IsSeeding { get; set; }
    public string SeedingUrl { get; set; }
    public Nullable<int> TotalImpression { get; set; }
    public Nullable<int> TotalLikeCount { get; set; }
    public Nullable<int> TotalCommentCount { get; set; }
    public Nullable<int> Duration { get; set; }
    public string ShortDescription { get; set; }
    public string DurationText { get; set; }
    public MetaInfo MetaInfo { get; set; }
    public string EmbedUrl { get; set; }
    public string SeoUrl { get; set; }
}

public partial class VideoJson 
{
    public string Id { get; set; }
    public List<VideoAncestor> Ancestors { get; set; }
    public string Title { get; set; }
    public string Text { get; set; }
    public int IId { get; set; }
    public string Description { get; set; }
    public string Status { get; set; }
    public List<string> Tags { get; set; }
    public DateTime CreatedDate { get; set; }
    public int Duration { get; set; }
    public int ViewCount { get; set; }
    public string Url { get; set; }
    public List<File> Files { get; set; }
}

public class File
{
    public string FileName { get; set; }
    public string ContentType { get; set; }
    public DateTime ModifiedDate { get; set; }
    public int Length { get; set; }
    public int ChunkSize { get; set; }
    public string _Id { get; set; }
    public string id { get; set; }
    public string Application { get; set; }

}

public class Metadata
{
    public string Title { get; set; }
    public string Description { get; set; }
    public bool FileShowRelatedNews { get; set; }
    public string _Id { get; set; }
    public string id { get; set; }
    public string Application { get; set; }
    public List<string> Tags { get; set; }
}

public class TextTag
{
    public string IxName { get; set; }
    public string Name { get; set; }
    public long Order { get; set; }
}

public class ContentTarget
{
    public string IxName { get; set; }
    public string Name { get; set; }
}

public class VideoAncestor
{
    public string Id { get; set; }
    public string ContentType { get; set; }
    public string Title { get; set; }
    public string Path { get; set; }
    public string IxName { get; set; }
    public string SelfPath { get; set; }
    public string Url { get; set; }
}

public class MetaInfo
{
    public string Title { get; set; }
    public string Keywords { get; set; }
    public string Description { get; set; }
}

public class VideoList
{
    public List<Video> Videos { get; set; }
    public int TotalCount { get; set; }

    public VideoList()
    {
        Videos = new List<Video>();
    }
}
