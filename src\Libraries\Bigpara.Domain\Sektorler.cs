﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Bigpara.Domain;

[Serializable]
[Table("tblSektorler")]
public partial class Sektorler : BaseEntity
{

    [Key]
    [Column("strKod")]
    public string StrKod { get; set; }

    [Column("strAd")]
    public string? StrAd { get; set; }

    [Column("strSektorKodu")]
    public string? StrSektorKodu { get; set; }

    [Column("strSektorId")]
    public string? StrSektorId { get; set; }
}