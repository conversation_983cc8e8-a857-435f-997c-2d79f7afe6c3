﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Core.Dtos
{
    public record PaginationRequest(int PageSize = 10, int PageIndex = 0);

    public class PaginatedItems<TEntity>(int pageIndex, int pageSize, long count, IEnumerable<TEntity> data) where TEntity : class
    {
        public int PageIndex { get; } = pageIndex;

        public int PageSize { get; } = pageSize;

        public long Count { get; } = count;

        public IEnumerable<TEntity> Data { get; } = data;
    }
}
