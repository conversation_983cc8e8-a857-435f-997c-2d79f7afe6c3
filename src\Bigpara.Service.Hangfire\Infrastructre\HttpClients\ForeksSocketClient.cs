﻿using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructre.HttpClients.Interfaces;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Net.WebSockets;
using System.Text;

namespace Bigpara.Service.Hangfire.Infrastructre.HttpClients
{
    public class ForeksSocketClient : IForeksSocketClient
    {
        private ClientWebSocket _webSocket;
        private Timer _heartbeatTimer;
        private Timer _reconnectTimer;
        private CancellationTokenSource _cancellationTokenSource;

        public bool _isLoggedIn = false;
        private bool _isReconnecting = false;

        // Bağlantı bilgilerini sakla
        private string _socketAddress;
        private string _username;
        private string _password;
        private readonly object _lockObject = new object();

        // Yeniden bağlantı ayarları
        private int _reconnectAttempts = 0;
        private const int MAX_RECONNECT_ATTEMPTS = 10;
        private const int RECONNECT_DELAY_MS = 5000; // 5 saniye
        private const int MAX_RECONNECT_DELAY_MS = 60000; // 60 saniye

        // Event delegates
        public event EventHandler<ResponseArgs> OnConnected;
        public event Func<object, ResponseArgs, Task> OnLogin;
        public event Func<object, ResponseArgs, Task> OnData;
        public event EventHandler<ErrorEventArgs> OnError;
        public event EventHandler<ResponseArgs> OnDisconnected;
        public event EventHandler<ResponseArgs> OnSubscriptionConfirmed;

        public bool IsConnected => _webSocket?.State == WebSocketState.Open;
        public bool IsLogin => _isLoggedIn;

        public async Task ConnectAsync(string socketAddress)
        {
            _socketAddress = socketAddress;
            _cancellationTokenSource = new CancellationTokenSource();

            await InternalConnectAsync();
        }

        private async Task InternalConnectAsync()
        {
            try
            {
                // Mevcut bağlantıyı temizle
                CleanupConnection();

                _webSocket = new ClientWebSocket();
                var wsUri = new Uri(_socketAddress);

                Console.WriteLine($"WebSocket bağlantısı kuruluyor: {wsUri}");
                await _webSocket.ConnectAsync(wsUri, _cancellationTokenSource.Token);

                if (_webSocket.State == WebSocketState.Open)
                {
                    Console.WriteLine("✅ WebSocket bağlantısı başarıyla kuruldu.");
                    _reconnectAttempts = 0; // Başarılı bağlantı sonrası reset

                    // Heartbeat zamanlayıcısını başlat
                    _heartbeatTimer = new Timer(SendHeartbeat, null, 0, 10000);

                    OnConnected?.Invoke(this, new ResponseArgs { Success = true });
                }
                else
                {
                    throw new Exception("WebSocket bağlantısı açılamadı.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Bağlantı hatası: {ex.Message}");
                OnError?.Invoke(this, new ErrorEventArgs(ex));

                if (!_cancellationTokenSource.IsCancellationRequested)
                {
                    await ScheduleReconnect();
                }
            }
        }

        public async Task LoginAsync(string username, string password)
        {
            _username = username;
            _password = password;

            Console.WriteLine($"🔐 Giriş denemesi - Kullanıcı: {username}");

            var loginMessage = new
            {
                _id = 64,
                user = username,
                password = password,
                info = JsonConvert.SerializeObject(new
                {
                    company = "FOREKS",
                    resource = "default",
                    platform = "webSocket",
                    appVersion = "1.0.1",
                    appName = "hurriyetbigpara_webfeed"
                }),
                resource = "fxplus"
            };

            await SendDataAsync(loginMessage);
        }

        private void SendHeartbeat(object state)
        {
            if (_webSocket?.State == WebSocketState.Open && _isLoggedIn)
            {
                Console.WriteLine("💓 Heartbeat gönderildi");
                var heartbeatMessage = new { _id = 16 };
                _ = Task.Run(async () => await SendDataAsync(heartbeatMessage));
            }
        }

        public async Task Listen()
        {
            var buffer = new byte[4096];

            try
            {
                while (_webSocket?.State == WebSocketState.Open && !_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    var result = await _webSocket.ReceiveAsync(
                        new ArraySegment<byte>(buffer),
                        _cancellationTokenSource.Token
                    );

                    if (result.MessageType == WebSocketMessageType.Close)
                    {
                        Console.WriteLine("🚪 WebSocket bağlantısı kapatılıyor.");
                        await HandleDisconnection();
                        break;
                    }

                    var message = Encoding.UTF8.GetString(buffer, 0, result.Count);
                    await HandleResponse(message);
                }
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("Listen işlemi iptal edildi.");
            }
            catch (WebSocketException ex)
            {
                Console.WriteLine($"❌ WebSocket hatası: {ex.Message}");
                OnError?.Invoke(this, new ErrorEventArgs(ex));
                await HandleDisconnection();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Listen hatası: {ex.Message}");
                OnError?.Invoke(this, new ErrorEventArgs(ex));
                await HandleDisconnection();
            }
        }

        private async Task HandleDisconnection()
        {
            lock (_lockObject)
            {
                if (_isReconnecting)
                    return;

                _isReconnecting = true;
            }

            _isLoggedIn = false;
            OnDisconnected?.Invoke(this, new ResponseArgs { Success = false });

            if (!_cancellationTokenSource.IsCancellationRequested)
            {
                await ScheduleReconnect();
            }

            _isReconnecting = false;
        }

        private async Task ScheduleReconnect()
        {
            if (_reconnectAttempts >= MAX_RECONNECT_ATTEMPTS)
            {
                Console.WriteLine($"❌ Maksimum yeniden bağlantı denemesi ({MAX_RECONNECT_ATTEMPTS}) aşıldı.");
                OnError?.Invoke(this, new ErrorEventArgs(new Exception("Maksimum yeniden bağlantı denemesi aşıldı.")));
                return;
            }

            _reconnectAttempts++;

            // Exponential backoff ile bekleme süresi
            var delay = Math.Min(RECONNECT_DELAY_MS * Math.Pow(2, _reconnectAttempts - 1), MAX_RECONNECT_DELAY_MS);

            Console.WriteLine($"🔄 Yeniden bağlantı denemesi #{_reconnectAttempts} - {delay}ms sonra...");

            _reconnectTimer = new Timer(async _ =>
            {
                try
                {
                    await InternalConnectAsync();

                    if (IsConnected && !string.IsNullOrEmpty(_username) && !string.IsNullOrEmpty(_password))
                    {
                        await LoginAsync(_username, _password);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Yeniden bağlantı hatası: {ex.Message}");
                    OnError?.Invoke(this, new ErrorEventArgs(ex));
                }
            }, null, (int)delay, Timeout.Infinite);
        }

        private async Task HandleResponse(string message)
        {
            try
            {
                var jsonMessage = JObject.Parse(message);

                // Giriş başarılı kontrolü
                if (jsonMessage["_id"]?.Value<int>() == 65)
                {
                    _isLoggedIn = true;
                    Console.WriteLine("✅ Giriş başarılı");
                    await OnLogin?.Invoke(this, new ResponseArgs { Result = "Giriş başarılı", Success = true });
                }
                // Giriş hatası kontrolü
                else if (jsonMessage["_id"]?.Value<int>() == 0)
                {
                    _isLoggedIn = false;
                    Console.WriteLine("❌ Giriş başarısız");
                    await OnLogin?.Invoke(this, new ResponseArgs { Result = "Giriş başarısız", Success = false });
                }

                // Veri mesajı kontrolü (örnek: _i ve l alanları varsa)
                if (jsonMessage["_i"] != null)
                {
                    var data = jsonMessage.ToDictionary();
                    await OnData?.Invoke(this, new ResponseArgs { Result = data, Success = true });
                }
            }
            catch (Exception ex)
            {
                OnError?.Invoke(this, new ErrorEventArgs(ex));
            }
        }

        public async Task SendDataAsync(object data)
        {
            if (_webSocket?.State == WebSocketState.Open)
            {
                try
                {
                    string jsonMessage = JsonConvert.SerializeObject(data);
                    var bytes = Encoding.UTF8.GetBytes(jsonMessage);
                    await _webSocket.SendAsync(
                        new ArraySegment<byte>(bytes),
                        WebSocketMessageType.Text,
                        true,
                        _cancellationTokenSource.Token
                    );
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Veri gönderme hatası: {ex.Message}");
                    OnError?.Invoke(this, new ErrorEventArgs(ex));
                    await HandleDisconnection();
                }
            }
        }

        public async Task Subscribe(Dictionary<string, string> stocks, List<string> fields)
        {
            var pageSize = 750;
            var pageCount = stocks.Count / pageSize;
            if (stocks.Count % pageSize > 0)
            {
                pageCount++;
            }

            for (int i = 0; i < pageCount; i++)
            {
                var subscribeMessage = new
                {
                    _id = 1,
                    id = 1,
                    symbols = stocks.Skip(i * pageSize).Take(pageSize).Select(x => x.Key).ToList(),
                    fields = fields
                };
                await SendDataAsync(subscribeMessage);
            }
        }

        private void CleanupConnection()
        {
            _heartbeatTimer?.Dispose();
            _reconnectTimer?.Dispose();

            if (_webSocket?.State == WebSocketState.Open)
            {
                _webSocket?.CloseAsync(WebSocketCloseStatus.NormalClosure, "Closing", CancellationToken.None);
            }

            _webSocket?.Dispose();
        }

        public Task UnSubscribe(List<string> symbols)
        {
            throw new NotImplementedException();
        }

        public Task DisconnectAsync()
        {
            _cancellationTokenSource?.Cancel();
            CleanupConnection();
            return Task.CompletedTask;
        }

        public void Dispose()
        {
            _cancellationTokenSource?.Cancel();
            CleanupConnection();
            _cancellationTokenSource?.Dispose();
        }
    }
}