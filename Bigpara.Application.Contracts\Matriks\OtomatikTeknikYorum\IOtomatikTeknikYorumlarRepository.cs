﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Core.Application.Contracts.Matriks.OtomatikTeknikYorum;

public interface IOtomatikTeknikYorumlarRepository
{
    Task<List<OtomatikTeknikYorumlar>> GetOtomatikTeknikYorumlars(string sembol, DateTime dateTime);
    Task<int> CreateOrUpdateOtomatikTeknikYorumlar(OtomatikTeknikYorumlar otomatikTeknikYorumlar);
    Task<List<OtomatikTeknikYorumlar>> GetOtomatikTeknikYorumlars(string sembol);
}
