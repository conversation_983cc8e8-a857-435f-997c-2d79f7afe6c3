﻿using Bigpara.Jobs.Hangfire.Workers;
using Bigpara.Service.Hangfire.Infrastructure.Data;
using Bigpara.Service.Hangfire.Services;
using Bigpara.Service.Hangfire.Services.Interfaces;
using Bigpara.Service.Hangfire.Services.Configuration;
using Bigpara.Notifications.Extensions;
using Bigpara.Cache.Redis.Extensions;
using Bigpara.Cache.Memory.Extensions;
using Bigpara.Persistence.Matriks.Borsa;
using Bigpara.Persistence.Matriks.Grafiks;
using Bigpara.Persistence.Matriks.Sektor;
using Bigpara.Persistence.Matriks.Sembol;
using Bigpara.Core.Application.Contracts.Matriks.Sektor;
using Bigpara.Core.Application.Contracts.Matriks.Sembol;
using Bigpara.Core.Application.Contracts.Matriks.Borsa;
using Bigpara.Core.Application.Contracts.Matriks.Grafiks;
using Bigpara.Core.Application.Contracts.Yuzeysels;
using Bigpara.Persistence.Matriks.Yuzeysels;
using Bigpara.Persistence;
using Bigpara.Persistence.SqlServer.Extensions;
using Bigpara.Core.Application.Common;
using Bigpara.Core.Application.Contracts.Bitcoin;
using Bigpara.Core.Application.Contracts.Matriks.PiyasaTakvim;
using Bigpara.Persistence.Matriks.PiyasaTakvim;
using Bigpara.Core.Application.Contracts.Matriks.SeansRaporlari;
using Bigpara.Persistence.Matriks.SeansRaporlari;
using Bigpara.Core.Application.Contracts.Matriks.Indikators;
using Bigpara.Persistence.Matriks.Indikators;
using Bigpara.Persistence.Bitcoin;
using Bigpara.Core.Application.Contracts.SystemSettings;
using Bigpara.Persistence.SystemSettings;
using Bigpara.Core.Application.Contracts.Matriks.OtomatikTeknikYorum;
using Bigpara.Persistence.Matriks.OtomatikTeknikYorum;
using Bigpara.Core.Application.Contracts.MetaData;
using Bigpara.Persistence.MetaData;
using Bigpara.Persistence.MongoDB.Extensions;
using Bigpara.Persistence.MongoDbContext;
using Bigpara.Core.Application.Contracts.Quark.Core;
using Bigpara.Persistence.Quark.Core;
using Bigpara.Logging.Extensions;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients.Interfaces;
using Bigpara.Service.Hangfire.Infrastructure.HttpClients;
using Bigpara.Service.Hangfire.Infrastructure.Publishers;
using Bigpara.Persistence.BigparaDB.Users;
using Bigpara.Core.Application.Contracts.BigparaDB.Users;

namespace Bigpara.Service.Hangfire.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddBigparaServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddCustomSqlServer<IMatriksDbContext, MatriksDbContext>(configuration, "Matriks");
        services.AddCustomSqlServer<IBigparaDbContext, BigparaDbContext>(configuration, "Bigpara");
        services.AddCustomMongoDB(configuration, "Mongo");
      
        services.AddScoped<IMongoDbContext, MongoDbContext>();
        services.AddScoped(typeof(IGenericRepository<,>), typeof(GenericRepository<,>));
        services.AddScoped<IPublisher, RedisPublisher>();
        services.AddScoped<IForeksSocketClient, ForeksSocketClient>();
        services.AddScoped<IStoredProcedureParameterService, StoredProcedureParameterService>();
        services.AddScoped<ISymbolService, SymbolService>();
        services.AddScoped<IBorsaService, BorsaService>();
        services.AddScoped<IPariteService, PariteService>();
        services.AddScoped<IViopService, ViopService>();
        services.AddScoped<ISerbestPiyasaService, SerbestPiyasaService>();
        services.AddScoped<IGelecekAndEndeksService, GelecekAndEndeksService>();
        services.AddScoped<ISectorService, SectorService>();
        services.AddScoped<IBolunmeService, BolunmeService>();
        services.AddScoped<ITahvilService, TahvilService>();

        services.AddScoped<IEndeksService, EndeksService>();
        services.AddScoped<IEndeksRepository, EndeksRepository>();

        services.AddScoped<ISektorService, SektorService>();
        services.AddScoped<ISektorRepository, SektorRepository>();

        services.AddScoped<ISembollerService, SembollerService>();
        services.AddScoped<ISembolRepository, SembolRepository>();

        services.AddScoped<IIndikatorlerService, IndikatorlerService>();
        services.AddScoped<IIndikatorRepository, IndikatorRepository>();

        services.AddScoped<IYuzeyselService, YuzeyselService>();
        services.AddScoped<IYuzeyselRepository, YuzeyselRepository>();

        services.AddScoped<IGrafikService, GrafikService>();
        services.AddScoped<IGrafikRepository, GrafikRepository>();

        services.AddScoped<IHisseService, HisseService>();
        services.AddScoped<IHisseRepository, HisseRepository>();

        services.AddScoped<IOranlarSektorelService, OranlarSektorelService>();
        services.AddScoped<IOranlarSektorelRepository, OranlarSektorelRepository>();

        services.AddScoped<IBitcoinService, BitcoinService>();
        services.AddScoped<IBitcoinRepository, BitcoinRepository>();

        services.AddScoped<IPiyasaTakvimiService, PiyasaTakvimiService>();
        services.AddScoped<IPiyasaTakvimiRepository, PiyasaTakvimiRepository>();

        services.AddScoped<ISeansRaporlariService, SeansRaporlariService>();
        services.AddScoped<ISeansRaporlariRepository, SeansRaporlariRepository>();

        services.AddScoped<IGecmisKapanisRepository, GecmisKapanisRepository>();

        services.AddScoped<ISystemSettingsRepository, SystemSettingsRepository>();
        services.AddScoped<ISystemSettingsService, SystemSettingsService>();

        services.AddScoped<IOtomatikTeknikYorumlarRepository, OtomatikTeknikYorumlarRepository>();
        services.AddScoped<IOtomatikTeknikYorumlarService, OtomatikTeknikYorumlarService>();

        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IUserService, UserService>();

        services.AddScoped<IMetaDataRepository, MetaDataRepository>();
        services.AddScoped<IMetaDataService, MetaDataService>();

        services.AddScoped<ICmsRepository, CmsRepository>();
        services.AddScoped<ICmsService, CmsService>();

        services.AddSingleton<FeedConfigurationService>();

        if (configuration.GetValue("RealTimeDataActive", true))
        {
            services.AddHostedService<ForeksRealTimeBackgroundService>();
        }

        services.AddCustomMemoryCache();
        services.AddCustomRedisCache(configuration);
        services.AddNotifications(configuration);
        services.UseCustomLogging(configuration);

        return services;
    }


}
