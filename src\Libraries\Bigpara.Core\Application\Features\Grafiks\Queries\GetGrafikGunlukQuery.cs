﻿using Bigpara.Core.Application.Contracts.Matriks.Grafiks;
using Bigpara.Core.Application.Features.Grafiks.Queries.ViewModels;
using MediatR;

namespace Bigpara.Core.Application.Features.Grafiks.Queries;




#region Query
public class GetGrafikGunlukQuery : IRequest<GrafikGunlukViewModel>
{
    public List<string> Symbols { get; set; }
    public int PerCount { get; set; }
    public int BackDay { get; set; }
}
#endregion

#region Handlers

public class GetGrafikGunlukQueryHandler : IRequestHandler<GetGrafikGunlukQuery, GrafikGunlukViewModel>
{
    private readonly IGrafikRepository _grafikGunlukRepository;
    public GetGrafikGunlukQueryHandler(IGrafikRepository grafikGunlukRepository)
    {
        _grafikGunlukRepository = grafikGunlukRepository;
    }
    public async Task<GrafikGunlukViewModel> Handle(GetGrafikGunlukQuery request, CancellationToken cancellationToken)
    {
        var response = new GrafikGunlukViewModel();
        var data = await _grafikGunlukRepository.GetGrafikGunlukBySembolIds(string.Join(",", request.Symbols), request.PerCount, request.BackDay);

        foreach (var item in request.Symbols)
        {
            var grafikItem = new GrafikGunlukItemViewModel();
            grafikItem.Current = data.OrderByDescending(p => p.TARIH).FirstOrDefault(p => p.SEMBOL == item);
            grafikItem.Chart = data.Where(p => p.SEMBOL == item).ToList();
            response.Data.Add(grafikItem);
        }

        return response;
    }
}

#endregion



