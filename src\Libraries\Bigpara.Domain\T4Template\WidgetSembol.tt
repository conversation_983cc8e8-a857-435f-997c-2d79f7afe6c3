﻿<#@ template language="C#" hostspecific="True" debug="True" #>
<#@ output extension="cs" #>
<#@ assembly name="System.Core" #>
<#@ assembly name="System.Data" #>
<#@ assembly name="System.Xml" #>
<#@ assembly name="System.Linq" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ assembly name="EnvDTE" #>
<#@ import namespace="System" #>
<#@ import namespace="System.IO" #>
<#@ import namespace="System.Configuration" #>
<#@ import namespace="System.Text.RegularExpressions" #>

<#
    string connectionString = @"Data Source=***************;Initial Catalog=DBMATRIKSv2;Persist Security Info=True;User ID=*****;Password=*****;";
    SqlConnection sqlConnSections = new SqlConnection(connectionString);
    sqlConnSections.Open();

    string sqlSections = @"
        SELECT DISTINCT 
            dbo.SEMBOLLER.SEMBOLID, 
            dbo.SEMBOLLER.SEMBOL, 
            dbo.SEMBOLLER.ACIKLAMA, 
            dbo.SEMBOLLER.PIYASAID, 
            ISNULL(bp.SembollerEk.URL, '') AS URL 
        FROM dbo.SEMBOLLER 
        LEFT JOIN bp.SembollerEk 
            ON dbo.SEMBOLLER.SEMBOLID = bp.SembollerEk.SEMBOLID 
        WHERE (dbo.SEMBOLLER.AKTIF = 1) 
          AND (dbo.SEMBOLLER.PIYASAID IN (3, 4, 26, 24, 27)) 
        GROUP BY 
            dbo.SEMBOLLER.SEMBOLID, 
            dbo.SEMBOLLER.SEMBOL, 
            dbo.SEMBOLLER.ACIKLAMA, 
            dbo.SEMBOLLER.PIYASAID, 
            URL";

    SqlCommand sqlCommSections = new SqlCommand(sqlSections, sqlConnSections);
    IDataReader readerSections = sqlCommSections.ExecuteReader();
    List<SembolItem> owners = new List<SembolItem>();

    while (readerSections.Read())
    {
        owners.Add(new SembolItem()
        {
            SembolId = Convert.ToInt32(readerSections[0]),
            Name = readerSections[1].ToString(),
            Aciklama = readerSections[2].ToString(),
            PiyasaId = Convert.ToInt32(readerSections[3]),
            Url = readerSections[4].ToString()
        });
    }

    readerSections.Close();
    sqlCommSections.Dispose();
#>
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;

namespace Bigpara.Domain.Widget
{
    public class WidgetSembol
    {
        #region "Owner Class Definitions"
        <#
            foreach (SembolItem owner in owners)
            {
        #>
        public partial class <#= owner.Name.Replace(" ", "_") #>
        {
            public const int Id = <#= owner.SembolId #>;
            public const string Sembol = "<#= owner.Name #>";
            public const string Url = "<#= owner.Url #>";
            public const int PiyasaId = <#= owner.PiyasaId #>;
        }
        <#
            }
        #>
        #endregion
    }
}

<#+
    // Gömülü sınıf tanımı
    public class SembolItem
    {
        public int SembolId { get; set; }
        public string Name { get; set; }
        public string Aciklama { get; set; }
        public string Url { get; set; }
        public int PiyasaId { get; set; }
    }
#>
