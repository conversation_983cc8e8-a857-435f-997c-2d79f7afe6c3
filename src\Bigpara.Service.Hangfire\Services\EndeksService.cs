﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Bigpara.Persistence;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructre.Data;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services
{
    public class EndeksService : IEndeksService
    {
        private readonly IStoredProcedureParameterService _storedProcedureService;
        private readonly ISymbolService _symbolService;

        private readonly TimeZoneInfo _turkeyTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");

        public EndeksService
        (
            IStoredProcedureParameterService storedProcedureService,
            ISymbolService symbolService
        )
        {
            _storedProcedureService = storedProcedureService;
            _symbolService = symbolService;
        }

        public async Task Change(YuzeyselDto item)
        {
            if (item == null)
                return;

            var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

            var symbols = await _symbolService.GetSymbolByCodeAsync(item.SEMBOL);

            if (symbols == null)
            {
                return;
            }

            //symbols.LastChangeTime = item.UnixTime;
            item.Security = symbols.Sembol.ImkbHisseTipString;

            if (!string.IsNullOrWhiteSpace(item.Security) && !string.IsNullOrWhiteSpace(item.SEMBOL))
            {
                if (item.Security.Equals("V") && item.SEMBOL.EndsWith("V"))
                {
                    item.SEMBOL = item.SEMBOL.Substring(0, item.SEMBOL.Length - 1);
                }
            }

            item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
                _turkeyTimeZone);

            var endeksParameters = storedProcedureHelper.CheckImkbEndeksParameter(item);
            if (endeksParameters != null)
                await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbEndeks", endeksParameters, item.SEMBOL);

            var endeksToplamParameters = storedProcedureHelper.CheckImkbEndeksToplamParameter(item);
            if (endeksToplamParameters != null)
                await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbEndeksToplam", endeksToplamParameters, item.SEMBOL);

            await Task.CompletedTask;
        }

        public async Task ChangeRealTime(YuzeyselDto item)
        {
            if (item == null)
                return;

            var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

            var symbols = await _symbolService.GetSymbolByCodeAsync(item.SEMBOL);

            if (symbols == null)
            {
                return;
            }

            //symbols.LastChangeTime = item.UnixTime;
            item.Security = symbols.Sembol.ImkbHisseTipString;

            if (!string.IsNullOrWhiteSpace(item.Security) && !string.IsNullOrWhiteSpace(item.SEMBOL))
            {
                if (item.Security.Equals("V") && item.SEMBOL.EndsWith("V"))
                {
                    item.SEMBOL = item.SEMBOL.Substring(0, item.SEMBOL.Length - 1);
                }
            }

            item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
                _turkeyTimeZone);

            var endeksRealTimeParameters = storedProcedureHelper.CheckImkbEndeksRealTimeParameter(item);
            if (endeksRealTimeParameters != null)
                await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbEndeks_Realtime", endeksRealTimeParameters, item.SEMBOL);

            await Task.CompletedTask;
        }
    }
}
