﻿using Microsoft.EntityFrameworkCore;

namespace Bigpara.Persistence;

public interface IDbContext
{
    DbSet<TEntity> Set<TEntity>() where TEntity : class;
    IQueryable<TQuery> FromSql<TQuery>(string sql, params object[] parameters) where TQuery : class;
    //IQueryable<TEntity> EntityFromSql<TEntity>(string sql, params object[] parameters) where TEntity : class;
    //List<T> ExecSQL<T>(string query,params object[] parameters);
}
