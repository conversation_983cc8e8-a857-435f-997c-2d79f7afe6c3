﻿namespace Bigpara.Domain.Matriks;


public class EndeksYuzeysel
{
    public int SEMBOLID { get; set; }
    public string SEMBOL { get; set; }
    public string ACIKLAMA { get; set; }
    public DateTime? TARIH { get; set; }
    public double? ALIS { get; set; }
    public double? SATIS { get; set; }
    public double? ACILIS { get; set; }
    public double? YUKSEK { get; set; }
    public double? YUKSEK1 { get; set; }
    public double? YUKSEK2 { get; set; }
    public double? DUSUK { get; set; }
    public double? DUSUK1 { get; set; }
    public double? DUSUK2 { get; set; }
    public double? KAPANIS { get; set; }
    public double? KAPANIS1 { get; set; }
    public double? KAPANIS2 { get; set; }
    public decimal? HACIMLOT { get; set; }
    public decimal? HACIMLOT1 { get; set; }
    public decimal? HACIMLOT2 { get; set; }
    //public decimal? AORT { get; set; }
    //public decimal? AORT1 { get; set; }
    //public decimal? AORT2 { get; set; }
    public decimal? H<PERSON><PERSON><PERSON> { get; set; }
    public decimal? HACIMTL1 { get; set; }
    public decimal? HACIMTL2 { get; set; }
    public double? DUNKUKAPANIS { get; set; }
    public double? ONCEKIKAPANIS { get; set; }
    public double? IZAFIKAPANIS { get; set; }
    public double? TAVAN { get; set; }
    public double? TABAN { get; set; }
    public double? YILYUKSEK { get; set; }
    public double? YILDUSUK { get; set; }
    public double? AYYUKSEK { get; set; }
    public double? AYDUSUK { get; set; }
    public double? HAFTAYUKSEK { get; set; }
    public double? HAFTADUSUK { get; set; }
    public double? ONCEKIYILKAPANIS { get; set; }
    public double? ONCEKIAYKAPANIS { get; set; }
    public double? ONCEKIHAFTAKAPANIS { get; set; }
    public double? YILORTALAMA { get; set; }
    public double? AYORTALAMA { get; set; }
    public double? HAFTAORTALAMA { get; set; }
    public double? FIYATADIMI { get; set; }
    public double? LOTADET { get; set; }
    public double? KAYKAR { get; set; }
    public double? SERMAYE { get; set; }
    public double? SAKLAMAOR { get; set; }
    public double? XU100AG { get; set; }
    public int ARTAN { get; set; }
    public int AZALAN { get; set; }
    public int ARTAN1 { get; set; }
    public int AZALAN1 { get; set; }
    public int ARTAN2 { get; set; }
    public int AZALAN2 { get; set; }
    public double? NETKAR { get; set; }
    public double? NET { get; set; }
    public double? YUZDEDEGISIM { get; set; }
    public double? YUZDEDEGISIMS1 { get; set; }
    public double? YUZDEDEGISIMS2 { get; set; }
    public double? FIYATKAZ { get; set; }
    public double? PIYDEG { get; set; }
    public double? OZSERMAYE { get; set; }
    public int? DONEM { get; set; }
}