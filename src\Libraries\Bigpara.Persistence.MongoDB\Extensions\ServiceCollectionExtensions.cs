﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using MongoDB.Driver;

namespace Bigpara.Persistence.MongoDB.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddCustomMongoDB(
        this IServiceCollection services,
        IConfiguration configuration,
        string connectionStringName)
    {
        var connectionString = configuration.GetConnectionString(connectionStringName);
        var mongoUrl = MongoUrl.Create(connectionString);
        var client = new MongoClient(mongoUrl);

        services.AddSingleton<IMongoClient>(client);

        services.AddScoped<IMongoDatabase>(provider =>
        {
            var mongoClient = provider.GetRequiredService<IMongoClient>();
            return mongoClient.GetDatabase(mongoUrl.DatabaseName);
        });

        return services;
    }
}
