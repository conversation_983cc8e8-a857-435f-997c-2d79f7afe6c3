﻿using Bigpara.Core.Application.Contracts.Matriks.SeansRaporlari;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.StoredProcedureResults;
using Microsoft.Data.SqlClient;

namespace Bigpara.Persistence.Matriks.SeansRaporlari;

public class SeansRaporlariRepository : ISeansRaporlariRepository
{
    private readonly IBigparaDbContext _bigparaDbContext;
    private readonly IMatriksDbContext _matriksDbContext;

    public SeansRaporlariRepository
    (
        IBigparaDbContext bigparaDbContext,
        IMatriksDbContext matriksDbContext
    )
    {
        _bigparaDbContext = bigparaDbContext;
        _matriksDbContext = matriksDbContext;
    }

    public async Task<int> CreateSeansRaporu(SeansRaporu seansRaporu)
    {
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("sembol", seansRaporu.HisseAdi),
            new SqlParameter("desc", seansRaporu.HisseCumle),
            new SqlParameter("date", seansRaporu.EklenmeTarihi),
            new SqlParameter("direction", seansRaporu.Yon)
        };

        int result = await _bigparaDbContext.ExecuteNonQueryAsync("spSeansRaporuCreate", parameters.ToArray());

        return result;
    }


    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisseler()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu15DKBist0300EnCokDegerKaybedenHisseler");
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkBist0300EnCokDegerKazanaHisseler()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu15DKBist0300EnCokDegerKazanaHisseler");
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkIslemHacmi()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu10DKIslemHacmi");
    }

    public async Task<SeansRaporuYuzeysel> GetSeansRaporu10DkBist050Endeks()
    {
        return await _matriksDbContext.ExecuteQuerySingleAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu10DKBist050Endeks");
    }

    public async Task<SeansRaporuYuzeysel> GetSeansRaporu10DkBist030Endeks()
    {
        return await _matriksDbContext.ExecuteQuerySingleAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu10DKBist030Endeks");
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkEnCokIslemAdediSahipHisseler()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu10DKEnCokIslemAdediSahipHisseler");
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu1DkHisseAktivitiBilgisi()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu1DKHisseAktivitiBilgisi");
    }

    public async Task<SeansRaporuYuzeysel> GetSeansRaporuBistAcilisKapanis()
    {
        return await _matriksDbContext.ExecuteQuerySingleAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporuBistAcilisKapanis");
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkTavandaIslemGorenHisseler()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu15DKTavandaIslemGorenHisseler");
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkTabandaIslemGorenHisseler()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu15DKTabandaIslemGorenHisseler");
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkDegerKazananDikkatCeken()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu15DKDegerKazananDikkatCeken");
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkDegerKaybiDikkatCeken()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu15DKDegerKaybiDikkatCeken");
    }

    public async Task<List<SeansRaporuBistHisselerDurumIstatistik>> GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleri()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuBistHisselerDurumIstatistik>("bp.pGetSeansRaporu15DKArtanAzalanDegismeyenHisseAdetleri");
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkEnCokDegerKaybeden()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu10DKEnCokDegerKaybeden");
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkEnCokDegerKazanan()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<SeansRaporuYuzeysel>("bp.pGetSeansRaporu10DKEnCokDegerKazanan");
    }

    public async Task<SeansRaporuDoviz> GetSeansRaporu15DkDoviz(string sembol)
    {
        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("sembol", sembol)
        };

        return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<SeansRaporuDoviz>("bp.pGetSeansRaporu15DKDoviz", parameters.ToArray());
    }
}