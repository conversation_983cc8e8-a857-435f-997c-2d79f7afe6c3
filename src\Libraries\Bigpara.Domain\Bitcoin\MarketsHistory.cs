﻿namespace Bigpara.Domain.Bitcoin;

public partial class MarketsHistory
{
    public string Symbol { get; set; }
    public System.DateTime ValueDateTime { get; set; }
    public Nullable<double> OpenValue { get; set; }
    public Nullable<double> HighValue { get; set; }
    public Nullable<double> LowValue { get; set; }
    public Nullable<double> CloseValue { get; set; }
    public Nullable<double> VolumeBTC { get; set; }
    public Nullable<double> VolumeCurrency { get; set; }
    public Nullable<double> WeightedPrice { get; set; }
    public Nullable<System.DateTime> CreatedDateTime { get; set; }
}
