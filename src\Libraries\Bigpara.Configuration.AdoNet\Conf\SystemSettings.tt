﻿<#@ template language="C#" hostspecific="True" debug="True" #>
<#@ output extension="cs" #>
<#@ assembly name="System.Data" #>
<#@ assembly name="System.Xml" #>
<#@ import namespace="System.Data" #>
<#@ import namespace="System.Data.SqlClient" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ assembly name="EnvDTE" #>
<#@ assembly name="System.Configuration" #>
<#@ import namespace="System" #>
<#@ import namespace="System.Configuration" #>
<#@ import namespace="System.Text.RegularExpressions" #>
<#
	string connectionString = @"Data Source=***************;Initial Catalog=TestBigpara;Persist Security Info=True;User ID=*****;Password=*****;";

    SqlConnection sqlConn = new SqlConnection(connectionString);
    SqlConnection sqlConnSections = new SqlConnection(connectionString);
    sqlConn.Open();
    sqlConnSections.Open();
    SqlConnection sqlConnProp = new SqlConnection(connectionString);
    sqlConnProp.Open();

    string sqlSections = string.Format("SELECT distinct Owner  FROM SystemConfiguration");

    SqlCommand sqlCommSections = new SqlCommand(sqlSections, sqlConnSections);
    IDataReader readerSections = sqlCommSections.ExecuteReader();
    List<string> owners= new  List<string>();
    //System.Text.StringBuilder sbSections = new System.Text.StringBuilder();
    while (readerSections.Read())
    {
        owners.Add(readerSections[0].ToString());
    }

    readerSections.Close();
    sqlCommSections.Dispose();
  
#>
// This file is generated by t4 generator - SystemSettings.tt
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
namespace Bigpara.Configuration.AdoNet
{
	public static partial class SystemSettings
    {
		<#
		foreach(string owner in owners)
		{
	#>public static <#= owner #> <#= owner #> = new <#= owner #>();
		<#
		}
	#>	
	}

	#region " Owner Class Definations "
		<#
		foreach(string owner in owners)
		{
		#>	
    public class <#= owner #>
    {
        private readonly string _ownerName = "<#= owner #>";
	<#
        string sql = string.Format("SELECT ConfigKey, ParameterType, Description FROM SystemConfiguration where Owner=@Owner");
        SqlParameter p = new SqlParameter("Owner", owner);
        SqlCommand sqlComm = new SqlCommand(sql, sqlConn);
        sqlComm.Parameters.Add(p);
        IDataReader reader = sqlComm.ExecuteReader();
        
        while (reader.Read())
        {
	#>	
		/// <summary>
		/// <#= reader[2] #>
		/// </summary>
		public <#= reader[1] #> <#= reader[0] #>
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<<#= reader[1] #>>(_ownerName, "<#= reader[0] #>");
            }
        }
	<#
        }
        reader.Close();
        sqlComm.Dispose();
	#>
}	
<#
    }
#>
	#endregion
}