﻿using Bigpara.Core.Application.Contracts.Yuzeysels;
using Bigpara.Core.Application.Features.Tools.ViewModels;
using Bigpara.Domain.Matriks;
using MediatR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Core.Application.Features.Tools.Queries
{
    public class GetExchangeRatesQuery:IRequest<GetExchangeRatesQueryResponse>
    {
        public  string  Source { get; set; }
        public  decimal Amount { get; set; }
        public  string  TargetSymbols { get; set; }
    }

    public class GetExchangeRatesQueryHandler : IRequestHandler<GetExchangeRatesQuery, GetExchangeRatesQueryResponse>
    {
        private readonly IYuzeyselRepository _yuzeyselRepository;
        public GetExchangeRatesQueryHandler(IYuzeyselRepository yuzeyselRepository)
        {
            _yuzeyselRepository = yuzeyselRepository;
        }
        public async Task<GetExchangeRatesQueryResponse> Handle(GetExchangeRatesQuery request, CancellationToken cancellationToken)
        {
            

            var symbols = request.TargetSymbols.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
            if(!symbols.Contains(request.Source))
            {
                if(request.TargetSymbols.EndsWith(","))
                    request.TargetSymbols += request.Source;
                else
                    request.TargetSymbols += $",{request.Source}";
            }

            if (!symbols.Contains("TL"))
            {
                request.TargetSymbols += $",TL";
            }

            var marketData = await _yuzeyselRepository.GetBySembols(request.TargetSymbols);
            marketData.Add(new Domain.Matriks.Yuzeysel
            {
                SEMBOL = "TL",
                KAPANIS = 1.0,
                ALIS = 1.0,
                SATIS = 1.0,
            });

            // Kaynak varlığın değerini al
            var sourceAsset = marketData.FirstOrDefault(x => x.SEMBOL == request.Source);
            if (sourceAsset == null)
                throw new ArgumentException($"Source symbol '{request.Source}' not found");


            var response = new GetExchangeRatesQueryResponse();
            if (sourceAsset.SEMBOL == "TL")
            {
                response.Data.Add(new GetExchangeRatesQueryItemModel
                {
                    Symbol = request.Source,
                    AskPrice = sourceAsset.ALIS.GetValueOrDefault(1),
                    BidPrice = sourceAsset.SATIS.GetValueOrDefault(1),
                    Amount = request.Amount
                });
            }

            // Her hedef varlık için dönüştürme yap
            foreach (var targetSymbol in request.TargetSymbols.Split(","))
            {
                var targetAsset = marketData.FirstOrDefault(x => x.SEMBOL == targetSymbol);
                if (targetAsset == null)
                {
                    Console.WriteLine($"Warning: Target symbol '{targetSymbol}' not found");
                    continue;
                }

                // Dönüştürme formülü: (Kaynak Miktar * Kaynak Fiyat) / Hedef Fiyat
                var exchangeRate = Convert.ToDecimal(sourceAsset.SATIS / targetAsset.SATIS);
                var convertedValue = request.Amount * exchangeRate;

                response.Data.Add(new GetExchangeRatesQueryItemModel
                {
                    Symbol = targetSymbol,
                    AskPrice = targetAsset.ALIS.GetValueOrDefault(1),
                    BidPrice = targetAsset.SATIS.GetValueOrDefault(1),
                    Amount = Math.Round(convertedValue, 4),
                    Rate = Math.Round(exchangeRate, 6)
                });
            }



            return await Task.FromResult(response);
        }
    }
}
