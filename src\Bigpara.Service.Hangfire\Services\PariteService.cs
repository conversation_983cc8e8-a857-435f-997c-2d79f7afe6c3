﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructre.Data;
using Bigpara.Service.Hangfire.Services.Interfaces;
using Bigpara.Service.Hangfire.Infrastructre.HttpClients.Interfaces;
using Bigpara.Service.Hangfire.Infrastructre.Publishers;
using static Bigpara.Domain.Widget.WidgetSembol;

namespace Bigpara.Service.Hangfire.Services
{
    public class PariteService : IPariteService
    {

        private readonly IPublisher _publisher;
        private readonly ILogger<PariteService> _logger;
        private readonly IStoredProcedureParameterService _storedProcedureService;
        private readonly TimeZoneInfo _turkeyTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");

        public PariteService
        (
            IStoredProcedureParameterService storedProcedureService,
            ILogger<PariteService> logger,
            IPublisher publisher)
        {
            _storedProcedureService = storedProcedureService;
            _logger = logger;
            _publisher = publisher;
        }

        public async Task Change(YuzeyselDto item)
        {
            try
            {
                var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

                item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                    DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
                    _turkeyTimeZone);

                var fidesPariteParameters = storedProcedureHelper.CheckYuzeyselFidesPariteParameter(item);
                if (fidesPariteParameters != null)
                    await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_FidesParite", fidesPariteParameters, item?.SEMBOL);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Sembol işleme hatası: {item.SEMBOL}");
            }
        }

        public async Task ChangeRealTime(YuzeyselDto item)
        {
            try
            {
                var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

                item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                    DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
                    _turkeyTimeZone);

                var realTimePariteParameters = storedProcedureHelper.CheckYuzeyselFidesPariteRealTimeParameter(item);
                if (realTimePariteParameters != null)
                    await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_FidesParite_Realtime", realTimePariteParameters, item?.SEMBOL);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Sembol işleme hatası: {item.SEMBOL}");
            }
        }

        public async Task ChangeHistorical(YuzeyselDto item)
        {
            try
            {
                var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

                item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                    DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
                    _turkeyTimeZone);

                var fidesPariteHistoricalParameters = storedProcedureHelper.CheckYuzeyselFidesPariteHistoricalParameter(item);
                if (fidesPariteHistoricalParameters != null)
                    await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_FidesPariteTarihsel", fidesPariteHistoricalParameters, item?.SEMBOL);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Sembol işleme hatası: {item.SEMBOL}");
            }
        }

        public async Task SendRealTimeApi(Dictionary<string, string> values)
        {

            try
            {
                await _publisher.PublishSymbolDataAsync(values["SEMBOL"], values);
            }
            catch (Exception ex)
            {

                _logger.LogError(ex, $"Publish realtime error: {values["SEMBOL"]}", ex.Message, ex.StackTrace);
                
            }
        }
    }
}
