﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>17b8c065-1a10-40e4-9c00-d5cbf4b51ed6</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IdentityModel.AspNetCore.OAuth2Introspection" Version="6.2.0" />
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.17" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\bigpara.hurriyet.com.tr.ServiceDefaults\bigpara.hurriyet.com.tr.ServiceDefaults.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Cache.Memory\Bigpara.Cache.Memory.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Cache.Redis\Bigpara.Cache.Redis.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Cache\Bigpara.Cache.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.External.Quark\Bigpara.External.Quark.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Logging\Bigpara.Logging.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Persistence.SqlServer\Bigpara.Persistence.SqlServer.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Services\Bigpara.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Certs\IdentityServerCert.pfx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Certs\idsrv4.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
