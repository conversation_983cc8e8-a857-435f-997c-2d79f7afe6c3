﻿using Bigpara.Application.Common;
using Bigpara.Core.Application.Contracts.Matriks.Sektor;
using Bigpara.Domain;
using System.Threading.Tasks;

namespace Bigpara.Persistence.Matriks.Sektor
{
    public class SektorRepository : ISektorRepository
    {
        private readonly IGenericRepository<Sektorler,MatriksDbContext> _sectorRepository;

        public SektorRepository(IGenericRepository<Sektorler, MatriksDbContext> sectorRepository)
        {
            _sectorRepository = sectorRepository;
        }

        public virtual async Task<List<Sektorler>> GetSektorList()
        {
            var sektorler = (await _sectorRepository.GetAllAsync())
                .Where(s => !string.IsNullOrEmpty(s.StrKod))
                .ToList();

            return sektorler;
        }
    }
}
