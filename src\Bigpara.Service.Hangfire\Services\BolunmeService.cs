﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructre.Data;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class BolunmeService : IBolunmeService
{
    private readonly ILogger<BolunmeService> _logger;
    private readonly IStoredProcedureParameterService _storedProcedureService;
    private readonly ISymbolService _symbolService;

    private readonly TimeZoneInfo _turkeyTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");

    public BolunmeService
    (
        IStoredProcedureParameterService storedProcedureService,
        ILogger<BolunmeService> logger,
        ISymbolService symbolService
    )
    {
        _storedProcedureService = storedProcedureService;
        _logger = logger;
        _symbolService = symbolService;
    }

    public async Task Change(BolunmeDto bolunmeDto)
    {
        try
        {
            var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

            var symbol = await _symbolService.GetSymbolByLegacyCodeAsync(bolunmeDto.StrLegacyKod);
            if (symbol == null)
            {
                return;
            }

            bolunmeDto.StrKod = symbol.Sembol.Sembol;
            bolunmeDto.StrTarih = TimeZoneInfo.ConvertTimeFromUtc(
                DateTimeOffset.FromUnixTimeMilliseconds(bolunmeDto.UnixDate).UtcDateTime,
                _turkeyTimeZone);

            var bolunmeParameters = storedProcedureHelper.CheckBolunmeParameter(bolunmeDto);
            if (bolunmeParameters != null)
                await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_Bolunme", bolunmeParameters, bolunmeDto?.StrKod);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {bolunmeDto.StrKod}");
        }
    }
}