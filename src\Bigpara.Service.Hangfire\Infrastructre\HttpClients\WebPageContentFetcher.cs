﻿using Bigpara.Service.Hangfire.Infrastructre.HttpClients.Interfaces;
using OpenQA.Selenium;
using OpenQA.Selenium.Chrome;

namespace Bigpara.Service.Hangfire.Infrastructre.HttpClients
{
    public class WebPageContentFetcher : IWebPageContentFetcher, IDisposable
    {
        private readonly IWebDriver _driver;
        private readonly IJavaScriptExecutor _jsExecutor;

        public WebPageContentFetcher()
        {
            var options = new ChromeOptions();
            options.AddArgument("--headless"); // headless mod
            options.AddArgument("--no-sandbox");
            options.AddArgument("--disable-dev-shm-usage");

            _driver = new ChromeDriver(options);
            _jsExecutor = (IJavaScriptExecutor)_driver;
        }

        public async Task<string> GetWebPageBodyContent(string url)
        {
            if (string.IsNullOrEmpty(url))
            {
                return string.Empty;
            }

            using (var client = new HttpClient())
            {
                var response = await client.GetAsync(url);
                response.EnsureSuccessStatusCode();

                var htmlContent = await response.Content.ReadAsStringAsync();

                _driver.Navigate().GoToUrl("about:blank");

                _jsExecutor.ExecuteScript("document.write(arguments[0]);", htmlContent);

                var innerText = (string)_jsExecutor.ExecuteScript("return document.body ? document.body.innerText : '';");

                return innerText;
            }
        }

        public void Dispose()
        {
            _driver.Quit();
            _driver.Dispose();
        }
    }
}
