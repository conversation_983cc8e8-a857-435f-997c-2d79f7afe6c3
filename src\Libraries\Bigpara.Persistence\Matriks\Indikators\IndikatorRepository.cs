﻿using Bigpara.Core.Application.Contracts.Matriks.Indikators;
using Bigpara.Domain.Matriks;
using Microsoft.Data.SqlClient;

namespace Bigpara.Persistence.Matriks.Indikators;

public class IndikatorRepository : IIndikatorRepository
{
    private readonly IMatriksDbContext _matriksDbContext;

    public IndikatorRepository(IMatriksDbContext matriksDbContext)
    {
        _matriksDbContext = matriksDbContext;
    }

    private static List<SqlParameter> Parameters
    {
        get { return new List<SqlParameter>(); }
    }

    public async Task<int> CreateOrUpdate(Indikatorler indikator)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("SEMBOL", indikator.SEMBOL));
        parameters.Add(new SqlParameter("TARIH", indikator.TARIH));
        parameters.Add(new SqlParameter("UpdatedDateTime", indikator.UpdatedDateTime));
        parameters.Add(new SqlParameter("MOV_5", indikator.MOV_5));
        parameters.Add(new SqlParameter("MOV_9", indikator.MOV_9));
        parameters.Add(new SqlParameter("MOV_10", indikator.MOV_10));
        parameters.Add(new SqlParameter("MOV_20", indikator.MOV_20));
        parameters.Add(new SqlParameter("MOV_50", indikator.MOV_50));
        parameters.Add(new SqlParameter("MOV_100", indikator.MOV_100));
        parameters.Add(new SqlParameter("MOV_250", indikator.MOV_250));
        parameters.Add(new SqlParameter("BBW_20_2_Up", indikator.BBW_20_2_Up));
        parameters.Add(new SqlParameter("BBW_20_2_Middle", indikator.BBW_20_2_Middle));
        parameters.Add(new SqlParameter("BBW_20_2_Down", indikator.BBW_20_2_Down));
        parameters.Add(new SqlParameter("CCI_14", indikator.CCI_14));
        parameters.Add(new SqlParameter("MACD_26_12", indikator.MACD_26_12));
        parameters.Add(new SqlParameter("MACD_Trigger_9", indikator.MACD_Trigger_9));
        parameters.Add(new SqlParameter("Momentum_12", indikator.Momentum_12));
        parameters.Add(new SqlParameter("RSI_14", indikator.RSI_14));
        parameters.Add(new SqlParameter("FStoch_K_5", indikator.FStoch_K_5));
        parameters.Add(new SqlParameter("FStoch_D_3", indikator.FStoch_D_3));
        parameters.Add(new SqlParameter("SStoch_K_5_5", indikator.SStoch_K_5_5));
        parameters.Add(new SqlParameter("SStoch_D_3", indikator.SStoch_D_3));
        parameters.Add(new SqlParameter("ADX_3", indikator.ADX_3));
        parameters.Add(new SqlParameter("CCI_10", indikator.CCI_10));
        parameters.Add(new SqlParameter("CCI_20", indikator.CCI_20));
        parameters.Add(new SqlParameter("RSI_9", indikator.RSI_9));
        parameters.Add(new SqlParameter("SAR", indikator.SAR));
        parameters.Add(new SqlParameter("STC_5_1", indikator.STC_5_1));
        parameters.Add(new SqlParameter("STC_5_3", indikator.STC_5_3));
        parameters.Add(new SqlParameter("STC_13_1", indikator.STC_13_1));
        parameters.Add(new SqlParameter("STC_21_1", indikator.STC_21_1));


        return await _matriksDbContext.ExecuteNonQueryAsync("bp.pCreateOrUpdateIndikator", parameters.ToArray());
    }

    public async Task<Indikatorler> GetIndikatorSembolbyTarih(string sembol, DateTime dateTime)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembol", sembol));
        parameters.Add(new SqlParameter("tarih", dateTime));

        return await _matriksDbContext.ExecuteQuerySingleAsync<Indikatorler>("bp.pGetIndikatorSembolbyTarih", parameters.ToArray());
    }

    public async Task<List<Indikatorler>> GetIndikatorbyTarih(DateTime dateTime)
    {
        var parameters = Parameters;

        parameters.Add(new SqlParameter("tarih", dateTime));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<Indikatorler>("bp.pGetIndikatorlerByTarih", parameters.ToArray());
    }

    public async Task<Indikatorler> GetIndikatorbySembol(string sembol)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembol", sembol));

        return await _matriksDbContext.ExecuteQuerySingleAsync<Indikatorler>("bp.pGetIndikatorler", parameters.ToArray());
    }

    public async Task<List<Indikatorler>> GetIndikatorbyTarihSembol(string sembol, DateTime dateTime)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembol", sembol));
        parameters.Add(new SqlParameter("tarih", dateTime));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<Indikatorler>("bp.pGetIndikatorSembolbyTarih", parameters.ToArray());
    }

    public async Task<List<Indikatorler>> GetOtomatikTeknikYorumIndikatorSembolbyTarih(string sembol, DateTime dateTime)
    {
        var parameters = Parameters;
        parameters.Add(new SqlParameter("sembol", sembol));
        parameters.Add(new SqlParameter("tarih", dateTime));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<Indikatorler>("bp.pGetOtomatikTeknikYorumIndikatorSembolbyTarih", parameters.ToArray());
    }
}
