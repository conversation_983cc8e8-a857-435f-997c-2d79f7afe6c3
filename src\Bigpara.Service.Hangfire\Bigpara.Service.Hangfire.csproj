﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>4e98fb5b-2743-4dc6-9149-9faeb934878b</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..\..</DockerfileContext>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="EPPlus" Version="4.0.1.1" />
    <PackageReference Include="ForeksPubsubSdk" Version="2.1.5" />
    <PackageReference Include="Hangfire" Version="1.8.18" />
    <PackageReference Include="Hangfire.AspNetCore" Version="1.8.18" />
    <PackageReference Include="Hangfire.MemoryStorage" Version="1.8.1.1" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.MessagePack" Version="9.0.6" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Quark.NetStandard.Shared" Version="5.1.108" />
    <PackageReference Include="Selenium.WebDriver" Version="4.33.0" />
    <PackageReference Include="System.IO.Packaging" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\bigpara.hurriyet.com.tr.ServiceDefaults\bigpara.hurriyet.com.tr.ServiceDefaults.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Cache.Memory\Bigpara.Cache.Memory.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Cache.Redis\Bigpara.Cache.Redis.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Logging\Bigpara.Logging.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Notifications\Bigpara.Notifications.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Persistence.MongoDB\Bigpara.Persistence.MongoDB.csproj" />
    <ProjectReference Include="..\Libraries\Bigpara.Persistence.SqlServer\Bigpara.Persistence.SqlServer.csproj" />
  </ItemGroup>

</Project>
