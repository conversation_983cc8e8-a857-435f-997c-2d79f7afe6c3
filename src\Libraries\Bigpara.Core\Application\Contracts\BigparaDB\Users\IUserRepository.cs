﻿using Bigpara.Domain.Bigpara;

namespace Bigpara.Core.Application.Contracts.BigparaDB.Users;

public interface IUserRepository
{
    Task<User> RegisterUser(User user);
    Task<User> RegisterOrUpdateUser(User user);
    Task<int> UpdateUserLocation(User user);
    Task<int> InsertUserPaymentDetails(UserPayment details);
    Task UpdateUserLastLoginDate(User user);
    Task<User> GetUserByHurpassID(string hurpassID);
    Task<User> GetUserByEmail(string email);
    Task<int> DeleteUser(User user);
    Task<UserPayment> GetLicencedUserByHurpassID(string hurpassID);
    List<User> GetUserList(int pageIndex, int pageSize, out int totalItemCount);
    Task<User> GetUserByUsernamePassword(string username, string password);
    Task<int> LoginLogger(UserActivityLogger userActivityLogger);
    Task<int> CanliBorsaViewLog(UserActivityLogger userActivityLogger);
    Task<int> CanliBorsaFormFillLog(UserActivityLogger userActivityLogger);
    Task<bool> IsEmptyUserAddress(string hurpassID);
}
