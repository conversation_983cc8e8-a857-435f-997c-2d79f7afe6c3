﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Bigpara.Service.Hangfire.Models;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Bigpara.Service.Hangfire.Helpers;

public static class SembolHelper
{
    private static readonly string[] TargetIndices = { "XU100", "XU050", "XU030" };

    public static string GetStrPiyasa(string marketSector, string market, string securityType, string hisseTip, string domain)
    {
        var strPiyasa = string.Empty;
        try
        {
            if (domain == "BIST")
            {
                if (marketSector.Equals("Equity") && securityType.Equals("Stock") && hisseTip.Equals("W"))
                {
                    strPiyasa = "GOZALTI";
                }
                else if (marketSector.Equals("Equity") && securityType.Equals("Stock") && hisseTip.Equals("BE"))
                {
                    strPiyasa = "BIRINCIL";
                }
                else if (marketSector.Equals("Index") && securityType.Equals("EquityIndex"))
                {
                    strPiyasa = "IMKB_INX";
                }
                else if (marketSector.Equals("Equity") && securityType.Equals("Warrant"))
                {
                    strPiyasa = "ISEVarant";
                }
                else if (marketSector.Equals("Equity") && securityType.Equals("Fund") && hisseTip.Equals("F"))
                {
                    strPiyasa = "ISEFunds";
                }
                else if (marketSector.Equals("Equity") && securityType.Equals("Stock"))
                {
                    strPiyasa = "IMKB";
                }
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
        }
       
        return strPiyasa;
    }

    public static string GetStrEndeks(List<string> allIndices)
    {
        var strEndeks = string.Join("|", TargetIndices.Where(t => allIndices.Contains(t)));

        return strEndeks;
    }

    public static string FindStrBoard(string legacyCode)
    {
        if (string.IsNullOrEmpty(legacyCode) || legacyCode.Substring(0, 2) != "F_")
            return string.Empty;

        Match match = Regex.Match(legacyCode, @"\b(BFAN|CFAN|DFAN|EFAN|EVAN|FANA|KFAN|MFAN)\b", RegexOptions.IgnoreCase);
        if (match.Success)
        {
            return match.Value.ToUpper();
        }

        return string.Empty;
    }

    public static string GetMatchingSembollerString(string sembolStringFromService, List<SembolModel> semboller)
    {
        if (string.IsNullOrWhiteSpace(sembolStringFromService))
            return string.Empty;

        var sembolCodes = sembolStringFromService
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(x => x.Trim().ToUpperInvariant())
            .ToList();

        var matchingSemboller = semboller
            .Where(x => sembolCodes.Contains(x.Name.ToUpperInvariant()))
            .Select(x => x.Name)
            .ToList();

        return string.Join(",", matchingSemboller);
    }

    public static void AddRange(this Dictionary<string,string> target, Dictionary<string, string> source)
    {
        foreach (var pair in source)
        {
            if (!target.ContainsKey(pair.Key))
            {
                target.Add(pair.Key, pair.Value);
            }
        }
    }

    public static Dictionary<string, string> ToDictionary(this YuzeyselDto obj)
    {
        if (obj == null)
            throw new ArgumentNullException(nameof(obj));

        return obj
            .GetType()
            .GetProperties(BindingFlags.Public | BindingFlags.Instance)
            .Where(p => p.CanRead)
            .ToDictionary(
                prop => prop.Name,
                prop => prop.GetValue(obj, null)?.ToString() ?? null
            );
        
    }

}
