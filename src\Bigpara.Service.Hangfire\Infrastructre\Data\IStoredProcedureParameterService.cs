﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Microsoft.Data.SqlClient;
using System.Data.Common;

namespace Bigpara.Service.Hangfire.Infrastructre.Data;
public interface IStoredProcedureParameterService
{
    SqlParameter[] CreateSymbolStoredProcedureParameters(ForeksSembolDto sembollerDto);
    SqlParameter[] CreateViopSymbolStoredProcedureParameters(ForeksSembolDto sembollerDto);
    SqlParameter[] CreatePassiveSymbolStoredProcedureParameters(string strKod);
    SqlParameter[] CreateFidesPariteStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateFidesPariteRealTimeStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateFidesPariteHistoricalStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseAcilisStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseKapanisStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseSonStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseToplamStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseIstatistikStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseSonRealTimeStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseIstatistikRealTimeStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseOrtalamaStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseOrtalamaRealTimeStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseHistoricalStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateHisseMarjStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateGrandBazaarStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateGrandBazaarHistoricalStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateImkbVipSessionStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateImkbVipSessionHD1PStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateImkbViopHistoricalStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateTanimSektorlerStoredProcedureParameters(SektorlerDto sektorlerDto);
    SqlParameter[] CreateImkbEndeksStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[] CreateImkbEndeksToplamStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[]? CreateImkbEndeksRealTimeStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[]? CreateFidesGelecekStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[]? CreateFidesEndeksStoredProcedureParameters(YuzeyselDto yuzeyselDto);
    SqlParameter[]? CreateTahvilKesinOzetStoredProcedureParameters(TahvilDto tahvilDto);
    SqlParameter[]? CreateTahvilRepoOzetStoredProcedureParameters(TahvilDto tahvilDto);
    SqlParameter[]? CreateBolunmeStoredProcedureParameters(BolunmeDto bolunmeDto);
    SqlParameter[] UpdateSembolIfEmptylStoredProcedureParameters(ForeksSembolDto sembollerDto);
    Task ExecuteStoredProcedureAsync( string storedProcedureName, SqlParameter[] parameters, string symbolName);
    Task<List<T>> ExecuteStoredProcedureAsync<T>(string procedureName, params DbParameter[] parameters) where T : class, new();
}

