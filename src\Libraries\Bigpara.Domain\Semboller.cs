﻿using Bigpara.Domain.Matriks;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Bigpara.Domain;

[Serializable]
public class Semboller : BaseEntity
{

    [Column("SEMBOLID")]
    public int SembolId { get; set; } // SEMBOLID

    [Column("SEMBOL")]
    public string Sembol { get; set; } // SEMBOL

    [Column("ACIKLAMA")]
    public string? Aciklama { get; set; } // ACIKLAMA

    [Column("PIYASAID")]
    public byte? PiyasaId { get; set; } // PIYASAID

    [Column("ENDEKS")]
    public string? Endeks { get; set; } // ENDEKS

    [Column("IMKBPAZARKOD")]
    public char? ImkbPazarKod { get; set; } // IMKBPAZARKOD

    [Column("IMKBHISSETIP")]
    public char? ImkbHisseTip { get; set; } // IMKBHISSETIP

    [Column("SEKTORID")]
    public byte? SektorId { get; set; } // SEKTORID

    [Column("SEANSVAR")]
    public bool SeansVar { get; set; } // SEANSVAR

    [Column("DERINLIKVAR")]
    public bool DerinlikVar { get; set; } // DERINLIKVAR

    [Column("ONDALIKBASAMAK")]
    public byte? OndalikBasamak { get; set; } // ONDALIKBASAMAK

    [Column("AKTIF")]
    public bool Aktif { get; set; } // AKTIF

    [Column("HISSEGRUBU")]
    public char? HisseGrubu { get; set; } // HISSEGRUBU

    [Column("ISLEMTURU")]
    public char? IslemTuru { get; set; } // ISLEMTURU

    [Column("BRUTTAKAS")]
    public char? BrutTakas { get; set; } // BRUTTAKAS

    [Column("FOREKSKOD")]
    public string? ForeksKod { get; set; } // FOREKSKOD

    [Column("FOREKSKODYENI")]
    public string? ForeksKodyeni { get; set; } // FOREKSKODYENI

    [Column("SOCKETID")]
    public string? SocketId { get; set; } // SocketId

    [Column("MARKETSECTOR")]
    public string? MarketSector { get; set; } // MarketSector

    [Column("SECURITYTYPE")]
    public string? SecurityType { get; set; } // SecurityType



}