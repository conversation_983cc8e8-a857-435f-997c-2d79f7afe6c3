﻿using Bigpara.Core.Application.Common;

namespace Bigpara.Core.Application.Features.Tools.ViewModels
{
    public class GetExchangeRatesQueryItemModel
    {
        public decimal Amount { get; set; }
        public string Symbol { get; set; }
        public double BidPrice { get; set; }
        public double AskPrice { get; set; }
        public decimal Rate { get; set; }
    }

    public class GetExchangeRatesQueryResponse: BaseResponse
    {
        public GetExchangeRatesQueryResponse()
        {
            Data = new List<GetExchangeRatesQueryItemModel>();
        }
        public List<GetExchangeRatesQueryItemModel> Data { get; set; }
   
    }
}
