﻿using Bigpara.Configuration.AdoNet.DbProvider;
using Bigpara.Domain.Bigpara;
using System.Globalization;


namespace Bigpara.Configuration.AdoNet
{
    public static partial class SystemSettings
    {
        public static string GetSystemParameterDataKey(string owner, string configKey)
        {
            return $"s{SiteId}{owner}{configKey}";
        }

        static byte _siteId;
        public static byte SiteId
        {
            get
            {
                if (_siteId == 0)
                    _siteId = 1;// Convert.ToByte(ConfigurationManager.AppSettings["SiteId"] ?? "1");
                return _siteId;
            }
        }

        public static SystemParameterData GetSystemParameter(string ownerName, string settingsName)
        {
            SystemParameterData systemParameter = null;

            try
            {
                //  MemcachedManager memcachedManager = new MemcachedManager();
                systemParameter = MemoryCacheManager.Instance.Get<SystemParameterData>(GetSystemParameterDataKey(ownerName, settingsName));

                if (systemParameter == null)
                {
                    const int systemCacheParameters = 60 * 60;
                    systemParameter = GetSystemParameter(ownerName, settingsName, systemParameter);
                    MemoryCacheManager.Instance.Add(GetSystemParameterDataKey(ownerName, settingsName), systemParameter, systemCacheParameters);
                }
            }
            catch (Exception)
            {

            }

            if (systemParameter == null)
            {
                var parameters = new List<DbQueryParameter>
                {
                    new DbQueryParameter("owner", ownerName),
                    new DbQueryParameter("configKey", settingsName)
                };
                var systemConfiguration = MssqlDatabaseManager.Instance().ExecuteSingle<SystemConfiguration>("spGetSystemParameter", parameters, SqlConnectionMode.BIGPARA);
                if (systemConfiguration != null)
                {
                    systemParameter = new SystemParameterData
                    {
                        Value = systemConfiguration.ConfigValue,
                        Type = systemConfiguration.ParameterType
                    };
                }
            }
            return systemParameter;
        }

        private static SystemParameterData GetSystemParameter(string ownerName, string settingsName,
            SystemParameterData systemParameter)
        {
            if (systemParameter == null)
            {
                var parameters = new List<DbQueryParameter>
                {
                    new DbQueryParameter("owner", ownerName),
                    new DbQueryParameter("configKey", settingsName)
                };
                var systemConfiguration =
                    MssqlDatabaseManager.Instance()
                        .ExecuteSingle<SystemConfiguration>("spGetSystemParameter", parameters,
                            SqlConnectionMode.BIGPARA);
                if (systemConfiguration != null)
                {
                    systemParameter = new SystemParameterData
                    {
                        Value = systemConfiguration.ConfigValue,
                        Type = systemConfiguration.ParameterType
                    };
                }
            }
            return systemParameter;
        }

        public static T GetSystemSettingsValueByName<T>(string ownerName, string settingsName)
        {
            SystemParameterData systemParameter = GetSystemParameter(ownerName, settingsName);
            if (string.IsNullOrEmpty(systemParameter?.Value) || string.IsNullOrEmpty(systemParameter.Type))
            {
                return default;
            }

            Type parameterType = Type.GetType(systemParameter.Type, false);
            if (parameterType == null)
            {
                return default;
            }

            if (typeof(T) == typeof(DateTime))
            {
                DateTime valueAsDateTime;
                bool convertResult = DateTime.TryParseExact(systemParameter.Value, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out valueAsDateTime);
                if (convertResult)
                    return (T)(object)DateTime.ParseExact(systemParameter.Value, "dd/MM/yyyy", CultureInfo.InvariantCulture);

                convertResult = DateTime.TryParseExact(systemParameter.Value, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.None, out valueAsDateTime);
                if (convertResult)
                    return (T)(object)DateTime.ParseExact(systemParameter.Value, "dd/MM/yyyy HH:mm:ss", CultureInfo.InvariantCulture);

                convertResult = DateTime.TryParseExact(systemParameter.Value, "dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture, DateTimeStyles.None, out valueAsDateTime);
                if (convertResult)
                    return (T)(object)DateTime.ParseExact(systemParameter.Value, "dd/MM/yyyy HH:mm", CultureInfo.InvariantCulture);
            }

            return (T)Convert.ChangeType(systemParameter.Value, typeof(T), CultureInfo.InvariantCulture);
        }
    }
}
