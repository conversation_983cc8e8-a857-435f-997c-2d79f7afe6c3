﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Bigpara.Persistence;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructre.Data;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class GelecekAndEndeksService : IGelecekAndEndeksService
{
    private readonly ILogger<GelecekAndEndeksService> _logger;
    private readonly IStoredProcedureParameterService _storedProcedureService;

    private readonly TimeZoneInfo _turkeyTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");
    private readonly Dictionary<string, (string Code, string LegacyCode)> _dataMapping = new()
    {
        ["o410"] = ("BEL20", "BEL,NTS"),
        ["o408"] = ("AEX", "AEX"),
        ["o411"] = ("CAC40", "CAC"),
        ["o409"] = ("PSI20", "PSI20,LIS"),
        ["o430"] = ("AORD", "XAO,SYD"),
        ["o461"] = ("BRENT", "BRENT"),
        ["c113"] = ("PALLADIUM:CFD", "PALLUS,CFD"),
        ["c114"] = ("PLATINUM:CFD", "PLATUS,CFD"),
        ["c108"] = ("COPPERUS:CFD", "COPPUS,CFD"),
        ["c123"] = ("USCOCOA:CFD", "USCC,CFD"),
        ["c124"] = ("USCOFFEEC:CFD", "USCF,CFD"),
        ["c140"] = ("CORN:CFD", "USCORN,CFD"),
        ["c125"] = ("USCOTTON2:CFD", "USCT,CFD"),
        ["c134"] = ("HEATINGOIL:CFD", "HOILUS,CFD"),
        ["c144"] = ("WHEAT:CFD", "USWHT,CFD"),
        ["c145"] = ("USDX:CFD", "DX,CFD"),
        ["o458"] = ("TKC", "TKC,NYS"),
        ["o443"] = ("KLSE", "KLSE,MA"),
        ["o1702"] = ("AMX", "AMX"),
        ["o427"] = ("ATGD", "GD,ATH"),
        ["c126"] = ("USSUGAR11:CFD", "USSGR,CFD")
    };

    public GelecekAndEndeksService
    (
        IStoredProcedureParameterService storedProcedureService,
        ILogger<GelecekAndEndeksService> logger
    )
    {
        _storedProcedureService = storedProcedureService;
        _logger = logger;
    }

    public async Task Change(YuzeyselDto item)
    {
        try
        {
            if (_dataMapping.TryGetValue(item.SocketId, out var mappedValues))
            {
                item.SEMBOL = mappedValues.Code;
                item.LegacyCode = mappedValues.LegacyCode;
            }

            var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

            item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
                _turkeyTimeZone);

            if (item.SEMBOL != "BRENT")
            {
                var fidesGelecekParameters = storedProcedureHelper.CheckFidesGelecekParameter(item);
                if (fidesGelecekParameters != null)
                    await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_FidesGelecek", fidesGelecekParameters, item?.SEMBOL);
            }

            var fidesEndeksParameters = storedProcedureHelper.CheckFidesEndeksParameter(item);
            if (fidesEndeksParameters != null)
                await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_FidesEndeks", fidesEndeksParameters, item?.SEMBOL);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sembol işleme hatası: {item.SEMBOL}");
        }
    }
}