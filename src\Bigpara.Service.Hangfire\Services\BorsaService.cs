﻿using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Core.Application.Contracts.Matriks.Borsa;
using Bigpara.Core.Application.Contracts.Matriks.Sembol;
using Bigpara.Domain;
using Bigpara.Domain.Bigpara;
using Bigpara.Domain.Enums;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.Definitions;
using Bigpara.Domain.Matriks.StoredProcedureResults;
using Bigpara.Domain.SummaryTypes;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class BorsaService : IBorsaService
{
    #region IOC

    private readonly IRedisCacheService _redisCacheService;
    private readonly ISembolRepository _sembolRepository;
    private readonly IHisseRepository _hisseRepository;
    private readonly IGecmisKapanisRepository _gecmisKapanisRepository;
    private readonly IEndeksRepository _endeksRepository;
    private readonly ICacheService _cacheService;
    private readonly ISembollerService _sembollerService;
    #endregion


    public BorsaService
    (
        ISembolRepository sembolRepository,
        IHisseRepository hisseRepository,
        IGecmisKapanisRepository gecmisKapanisRepository,
        IEndeksRepository endeksRepository,
        IRedisCacheService redisCacheService,
        ICacheService cacheService,
        ISembollerService sembollerService
    )
    {
        _sembolRepository = sembolRepository;
        _hisseRepository = hisseRepository;
        _gecmisKapanisRepository = gecmisKapanisRepository;
        _endeksRepository = endeksRepository;
        _redisCacheService = redisCacheService;
        _cacheService = cacheService;
        _sembollerService = sembollerService;
    }

    public async Task<Semboller> GetSembollerById(int sembolId)
    {
        string key = string.Format(CacheKeys.SEMBOL_BY_ID_KEY, sembolId);

        return await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _sembolRepository.GetSembollerById(sembolId));
    }

    public async Task<List<SembolOzet>> GetEncokIslemArtanAzalanlarByType(ShareProcessStatusType type, int? topN)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_ENCOK_ARTAN_AZALANLAR_BY_ISLEMTIPI, (int)type);

        var data = (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_4DK_DURATION, () => _hisseRepository.GetHisseDurumByType(type))).ToList();
        if (topN.HasValue)
            return data.Take(topN.Value).ToList();

        return data;
    }

    public async Task<List<SembolOzet>> GetEncokIslemArtanAzalanlarByType(ShareProcessStatusType type, SeansTypes seansTypes)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_ENCOK_ISLEMLER_ARTAN_AZALANLAR_BY_ISLEMTIPI_SEANS, (int)type, (int)seansTypes);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_4DK_DURATION, () => _hisseRepository.GetHisseDurumByType(type, seansTypes))).ToList();
    }

    public async Task<List<SembolOzet>> GetHisseAgirlikliOrtalama(string direction, int pageNo, byte pageSize)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_AGILRLIK_ORTALAMA_BY_DIRECTION_PAGE_PAGESIZE_TOP, direction, pageNo, pageSize);

        return _redisCacheService.GetRedisList(key, () => _hisseRepository.GetAgirlikliOrtalamaDurum(direction, pageNo, pageSize).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_5DK_DURATION).ToList();
    }

    public List<SembolOzet> GetHisseAgirlikliOrtalamaByPaging(string orderby, int pageNo, byte pageSize, out int totalCount)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_AGILRLIK_ORTALAMA_BY_ORDERBY, orderby);

        var result = _redisCacheService.GetRedisList(key, () => _hisseRepository.GetAgirlikliOrtalamaDurumV2(orderby).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_5DK_DURATION, (short)pageNo, pageSize, out totalCount).ToList();
        return result.ToList();
    }

    public async Task<List<Hisse>> GetHisseList()
    {
        string key = string.Format(CacheKeys.HISSE_BY_ALL_KEY);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _hisseRepository.GetHisseList())).ToList();
    }

    public async Task<List<Hisse>> GetTumHisseList()
    {
        string key = string.Format(CacheKeys.SEMBOLLER_BY_ALL_KEY);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _hisseRepository.GetTumSemboller())).ToList();
    }

    public virtual async Task<List<HisseYuzeysel>> GetHisseYuzeysel()
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_YUZEYSEL_ALL_KEY);

        var result = (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, () => _hisseRepository.GetHisseYuzeysel())).ToList();
        return result.ToList();
    }

    public async Task<HisseYuzeysel> GetHisseYuzeyselDegerleriBySembolId(int sembolId)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_YUZEYSEL_SEMBOL_KEY_BYID, sembolId);

        return await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_4DK_DURATION, () => _hisseRepository.GetHisseYuzeyselDegerleriBySembolId(sembolId));
    }

    public async Task<HisseYuzeysel> GetHisseYuzeysel(string sembol)
    {

        var result = (await GetHisseYuzeysel()).FirstOrDefault(r => r.SEMBOL == sembol);
        if (result == null)
        {
            string key = string.Format(CacheKeys.BORSA_HISSE_YUZEYSEL_ALL_KEY);
            // _redisCacheService.Remove(key);
            _cacheService.Remove(key);
            result = (await GetHisseYuzeysel()).FirstOrDefault(r => r.SEMBOL == sembol);
        }
        return result;
    }

    public async Task<List<HisseYuzeysel>> GetHisseYuzeysel(Func<HisseYuzeysel, bool> filterFunction)
    {
        var result = (await GetHisseYuzeysel()).Where(filterFunction).ToList();
        return result.ToList();
    }

    public async Task<List<HisseYuzeyselOnline>> GetHisseYuzeyselOnline(Func<HisseYuzeyselOnline, bool> filterFunction)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_YUZEYSELONLINE_ALL_KEY);

        var result = (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, () => _hisseRepository.GetHisseYuzeyselOnline())).Where(filterFunction).ToList();
        return result;
    }

    public async Task<List<HisseYuzeyselIndikator>> GetHisseYuzeyselTarihsiz(string sembol)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_INDIKATOR_BY_SEMBOL, sembol);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, () => _hisseRepository.GetHisseYuzeyselTarihsiz(sembol))).ToList();
    }

    public async Task<List<HisseYuzeysel>> GetHisseYuzeyselTarihsizBySembolId(int sembolId)
    {
        return await GetHisseYuzeysel(r => r.SEMBOLID == sembolId);
    }

    public List<SembolOzet> GetGecmisKapanislar(out int totalCount, DateTime? selectedDate, MarketTypes marketType, char? selectedLetter, int pageNo, byte pageSize)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_ENDKES_GECMIS_KAPANISLAR_BY_DATA_MARKETTYPE, selectedDate.Value.ToString("yyyyMMdd"), (int)marketType, selectedLetter);

        var result = _redisCacheService.GetRedisList(key, () => _gecmisKapanisRepository.GetGecmisKapanisList(selectedDate, marketType, selectedLetter).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_5DK_DURATION, (short)pageNo, pageSize, out totalCount).ToList();
        return result;
    }

    public async Task<List<DunyaBorsalari>> GetDunyaBorsalari()
    {
        string key = string.Format(CacheKeys.BORSA_DUNYA_BORSALARI);

        var data = (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_5DK_DURATION, () => _hisseRepository.GetDunyaBorsalariData())).ToList();

        return data.ToList();
    }

    public async Task<List<DunyaBorsalari>> GetDunyaBorsalariByContinent(Continents continent)
    {
        string key = string.Format(CacheKeys.BORSA_DUNYA_BORSALARI);

        var data = (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_5DK_DURATION, () => _hisseRepository.GetDunyaBorsalariData())).ToList();
        return data.Where(c => c.SIRA1 == (int)continent).ToList();
    }

    public async Task<List<BilancoDonem>> GetBilancoDonems(string sembol, string donem, int cins)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_BILANCO_BY_SEMBOL_DONEM_CINS, sembol, donem, cins);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_10DK_DURATION, () => _hisseRepository.GetBilancoDonems(sembol, donem, cins))).ToList();
    }

    public async Task<List<BilancoDonem>> GetBilancoDonemByYil(string sembol, string donem, int cins, int yil)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_BILANCO_BY_SEMBOL_DONEM_CINS_YIL, sembol, donem, cins, yil);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_10DK_DURATION, () => _hisseRepository.GetBilancoDonemByYil(sembol, donem, cins, yil))).ToList();
    }

    public async Task<List<BilancoDonemSembol>> GetBilancoDonemsBySembol(string sembol)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_BILANCO_BY_SEMBOL, sembol);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_10DK_DURATION, () => _hisseRepository.GetBilancoDonemsBySembol(sembol))).ToList();
    }

    public async Task<List<EndeksYuzeysel>> GetEndeksYuzeysel()
    {
        string key = string.Format(CacheKeys.BORSA_ENDEKS_BY_ALL_KEY);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_5DK_DURATION, () => _endeksRepository.GetEndeksYuzeysel())).ToList();
    }

    public async Task<List<EndeksYuzeysel>> GetEndeksYuzeyselArtanAzalan()
    {
        string key = string.Format(CacheKeys.BORSA_ENDEKS_YUZEYSEL_ARTAN_AZALAN_BY_ALL_KEY);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_5DK_DURATION, () => _endeksRepository.GetEndeksYuzeyselArtanAzalan())).ToList();
    }

    public List<HisseYuzeysel> GetEndeksHisseYuzeysels(string endeks, Func<HisseYuzeysel, bool> filterFunction, short pageNo, int pageSize, out int totalCount)
    {
        totalCount = 0;
        string key = string.Format(CacheKeys.BORSA_ENDEKS_YUZYSEL_ALL_KEY, endeks);

        var getdata = new List<HisseYuzeysel>();
        if (_redisCacheService.ContainsKey(key))
        {
            var result = _redisCacheService.GetTypedList(key, filterFunction, pageNo, pageSize, out totalCount);
            getdata = (List<HisseYuzeysel>)result;
        }
        else
        {
            if (endeks == "XUTUM")
                endeks = string.Empty;
            var data = _hisseRepository.GetEndeksHisseYuzeysels(endeks).GetAwaiter().GetResult().OrderBy(r => r.SEMBOL);
            _redisCacheService.AddTypedList(key, data.AsEnumerable(), CacheKeys.CACHE_DATA_DURATION);
            var query = data.Where(filterFunction);
            totalCount = query.Count();
            getdata = query.Skip((pageNo - 1) * pageSize).Take(pageSize).ToList();
        }

        return getdata.ToList();
    }

    public async Task<List<HisseYuzeysel>> GetUserHisseYuzeysel(int userId)
    {
        //return _hisseRepository.GetUserHisseYuzeysel(userId);
        string key = string.Format(CacheKeys.BORSA_USER_HISSE_YUZEYSEL__BY_USERID, userId);

        return (await _cacheService.GetAsync(key, 10, () => _hisseRepository.GetUserHisseYuzeysel(userId))).ToList();
    }

    public async Task<List<HisseYuzeysel>> GetUserHisseYuzeyselOnline(int userId)
    {
        string key = string.Format(CacheKeys.BORSA_USER_HISSE_YUZEYSEL_ONLINE_BY_USERID, userId);

        return (await _cacheService.GetAsync(key, 10, () => _hisseRepository.GetUserHisseYuzeyselOnline(userId))).ToList();
    }

    public async Task<List<Haberler>> GetUserHisseHaberleri(int userId)
    {
        string key = string.Format(CacheKeys.BORSA_USER_HISSE_HABERLER__BY_USERID, userId);

        return _redisCacheService.GetRedisList(key, () => _hisseRepository.GetUserHisseHaberleri(userId).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_5DK_DURATION).ToList();
    }

    public async Task<List<SymbolNews>> GetUserSembolHisseHaberleri(int userId)
    {
        var key = string.Format(CacheKeys.BORSA_USER_SEMBOL_HISSE_HABERLER_BY_USERID, userId);
        var getUserSembolHaberleri = await _hisseRepository.GetUserSembolHaberleri(userId);
        var sembolNews = _cacheService.Get(key, CacheKeys.CACHE_DATA_5DK_DURATION, () =>
        {
            var sembolNewses = new List<SymbolNews>();
            //foreach (var sembolId in getUserSembolHaberleri.Select(r => r.SembolId).Distinct())
            //{
            //    var sm = _haberService.GetKapNewsBySymbolId(sembolId).GetAwaiter().GetResult().Select(p => new SymbolNews()
            //    {
            //        Id = p.Id,
            //        NewsType = p.NewsType,
            //        Sembol = _sembollerService.GetSembollerBySembolId(sembolId).GetAwaiter().GetResult().Sembol,
            //        StartDate = p.StartDate,
            //        Title = p.Title
            //    });
            //    sembolNewses.AddRange(sm);
            //}

            return sembolNewses.Where(r => getUserSembolHaberleri.Select(p => p.HaberId).Contains(r.Id)).OrderByDescending(r => r.StartDate).Take(5).ToList();

        });

        return sembolNews;
    }

    public async Task<List<LiveStockHisse>> GetYuzeselOnlineBySembol(DateTime? tarih)
    {
        return await _hisseRepository.GetYuzeselOnlineBySembol(tarih);
    }

    public async Task<List<HisseYuzeysel>> GetHisseFiyatlariByEndeksVeHarf(string endeks, int pageNo, byte pageSize, char? letter)
    {
        string key = string.Format("bigpara.canli.borsa.yuzeysel.by.endeks.pageno,pagesize.{0}-{1}-{2}-{3}-", endeks, pageNo, pageSize, letter);
        //TODO: Check
        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, async () => (await _hisseRepository.GetHisseFiyatlariByEndeksVeHarf(endeks, pageNo, pageSize, letter)).Data)).ToList();
    }

    public async Task<List<HisseYuzeysel>> GetHisseFiyatlariByEndeksVeHarfOnline(string endeks, int pageNo, byte pageSize, char? letter)
    {
        string key = string.Format("bigpara.canli.borsa.yuzeyselonline.by.endeks.pageno,pagesize.{0}-{1}-{2}-{3}-", endeks, pageNo, pageSize, letter);
        //TODO: Check
        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, async () => (await _hisseRepository.GetHisseFiyatlariByEndeksVeHarfOnline(endeks, pageNo, pageSize, letter)).Data)).ToList();
    }

    public async Task<List<EndeksYuzeysel>> GetEndeksDegerleriBySembol(string sembol)
    {
        return (await GetEndeksYuzeysel()).Where(r => r.SEMBOL == sembol).ToList();
    }

    public List<HisseYuzeysel> GetHisselerIslemHacmiArtanAzalan(OrderByDirection orderByDirection, int pageNo, byte pageSize, out int totalCount)
    {
        string key = string.Format(CacheKeys.ANALIZ_ISLEM_HACMI_BY_ORDERBY_KEY, (int)orderByDirection);

        return _redisCacheService.GetRedisList(key, () => _hisseRepository.GetHisselerIslemHacmiArtanAzalan(orderByDirection).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_5DK_DURATION, (short)pageNo, pageSize, out totalCount).ToList();
    }

    public List<HisseYuzeysel> GetHisselerIslemHacmiFiyatArtanAzalan(TradingVolumeFilterType tradingVolumeFilterType, int pageNo, byte pageSize, out int totalCount)
    {
        string key = string.Format(CacheKeys.BORSA_HISSE_YUZEYSEL_ISLEM_HACMI_FIYAT_ARTAN_AZALANLAR_BY_TIP, (int)tradingVolumeFilterType);

        return _redisCacheService.GetRedisList(key, () => _hisseRepository.GetHisselerIslemHacmiFiyatArtanAzalan(tradingVolumeFilterType).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_5DK_DURATION, (short)pageNo, pageSize, out totalCount).ToList();
    }

    public List<PerformansAnaliz> GetPerformansAnaliz(string endeks, int sembolId, DateTime? startDateTime, DateTime? endDateTime,
                                  string currency, int orderBy, int pageNo, int pageSize, out int totalCount)
    {

        string key = string.Empty;
        DateTime cacheDateTime;
        if (!endDateTime.HasValue)
        {
            cacheDateTime = DateTime.Now;
            key = string.Format(CacheKeys.BORSA_ANALIZ_PERFORMANS_BY_ENDEKS_SEMBOL_LASTDATE_CURRENCY_ORDER_TIP,
           endeks, sembolId, cacheDateTime.AddDays(-7).ToString("yyyyMMdd"), cacheDateTime.ToString("yyyyMMdd"), currency, orderBy);
        }
        else
        {
            cacheDateTime = endDateTime.Value;
            key = string.Format(CacheKeys.BORSA_ANALIZ_PERFORMANS_BY_ENDEKS_SEMBOL_DATE_CURRENCY_ORDER_TIP,
          endeks, sembolId, startDateTime?.ToString("yyyyMMdd") ?? cacheDateTime.AddDays(-7).ToString("yyyyMMdd"), cacheDateTime.ToString("yyyyMMdd"), currency, orderBy);
        }

        return _redisCacheService.GetRedisList(key, () => _hisseRepository.GetPerformansAnaliz(endeks, sembolId, startDateTime, endDateTime, currency, orderBy).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_5DK_DURATION, (short)pageNo, pageSize, out totalCount).ToList();
    }

    public async Task<List<KisaTanimSemboller>> GetUserHisseSembolList(int userId, int pageType)
    {
        return await _hisseRepository.GetUserHisseSembolList(userId, pageType);
    }

    public async Task<List<KisaTanimSemboller>> GetDefaultPiyasaBandi()
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_PIYASA_BANDI_DEFAULT);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, () => _hisseRepository.GetDefaultPiyasaBandi())).ToList();
    }

    public async Task<int> SaveUserHisse(int userId, int sembolId, int pageType, int opId)
    {
        return await _hisseRepository.SaveUserHisse(userId, sembolId, pageType, opId);
    }

    public async Task<int> SaveUserHisse(int userId, int sembolId, int pageType, int opId, int orderId)
    {
        return await _hisseRepository.SaveUserHisse(userId, sembolId, pageType, opId, orderId);
    }

    public async Task<List<KisaTanimSemboller>> GetPiyasaBandiHisseListe()
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_PIYASA_BANDI_DEFAULT_SHARED_LIST);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, () => _hisseRepository.GetPiyasaBandiHisseListe())).ToList();
    }

    public async Task<int> SaveUserAlert(int userId, string sembol, double price, string alertField)
    {
        return await _hisseRepository.SaveUserAlert(userId, sembol, price, alertField);
    }

    public async Task<int> UserAlertDeactivate(int userId, int id)
    {
        return await _hisseRepository.UserAlertDeactivate(userId, id);
    }

    public async Task<int> DeleteUserAlert(int userId, int sembolId)
    {
        return await _hisseRepository.DeleteUserAlert(userId, sembolId);
    }

    public async Task<List<UserAlert>> GetUserAlerts(int userId)
    {
        return await _hisseRepository.GetUserAlerts(userId);
    }

    public async Task<List<UserAlert>> GetUserAlertResult(int userId)
    {
        return await _cacheService.GetAsync(string.Format("bigpara.borsa.service.GetUserAlertResult.{0}", userId), CacheKeys.CACHE_DATA_DURATION, () => _hisseRepository.GetUserAlertResult(userId));
    }

    public async Task<int> UserAlertInstanceAlert(int userId)
    {
        return await _hisseRepository.UserAlertInstanceAlert(userId);
    }

    public async Task<List<YatirimAraclari>> GetYatirimAraclariDataList()
    {
        return (await _cacheService.GetAsync(CacheKeys.YATIRIMARACLARININGETIRILERI, CacheKeys.CACHE_DATA_10DK_DURATION, () => _sembolRepository.GetYatirimAraclariList())).ToList();
    }

    public async Task<List<SembolOzet>> GetHisseDurumByType(ShareProcessStatusType type, SeansTypes seansTypes)
    {
        return await _cacheService.GetAsync(string.Format("bigpara.borsa.service.gethissedurumbytype.{0}.{1}", (int)type, (int)seansTypes), CacheKeys.CACHE_DATA_5DK_DURATION, () => _hisseRepository.GetHisseDurumByType(type, seansTypes));
    }

    public async Task<SembolOzet> GetGecmisKapanisMaxDate()
    {
        return await _cacheService.GetAsync(CacheKeys.BORSA_HISSE_ENDKES_GECMIS_KAPANISLAR_BY_MAX_DATE, CacheKeys.CACHE_LONG_DATA_DURATION, () => _gecmisKapanisRepository.GetGecmisKapanisMaxDate());
    }
}