﻿using System.ComponentModel;

namespace Bigpara.Domain.Enums;

public enum NewsCategoryEnum
{

    [Description("Genel Haberler")]
    Genel = 1,

    [Description("Bitcoin Haberleri")]
    Bitcoin = 3,

    [Description("Kobi Haberleri")]
    <PERSON>bi = 4,

    [Description("Şirket Haberleri")]
    Sirket = 5,

    [Description("Bankacılık Haberleri")]
    Bankacilik = 6,

    [Description("Girişim Haberleri")]
    Girisim = 7,

    [Description("Teşvik Haberleri")]
    Tesvik = 8,

    [Description("Sektör Haberleri")]
    Sektor = 9,

    [Description("Piyasa Haberleri")]
    Piyasa = 10,

    [Description("Ekonomi Haberleri")]
    Ekonomi = 11,

    [Description("Politika Haberleri")]
    Politika = 12,

    [Description("Teknoloji Haberleri")]
    Teknoloji = 13,

    [Description("Döviz Haberleri")]
    Doviz = 14,

    [Description("Altın <PERSON>i")]
    Altin = 15,

    [Description("Faiz Haberleri")]
    Faiz = 16,

    [Description("Konut Haberleri")]
    Konut = 17,

    [Description("Kredi Haberleri")]
    Kredi = 18,

    [Description("Son Dakika Haberleri")]
    SonDakika = 20,

    [Description("Borsa Haberleri")]
    Borsa = 22,

    [Description("Borsa İstanbul Haberleri")]
    BorsaIstanbul = 23,

    [Description("Varant Haberleri")]
    Varant = 24,

    [Description("Araştırma Bölümü Haberleri")]
    ArastirmaBolumu = 25,

    [Description("Seo Haberleri")]
    SeoHaberleri = 26,

    [Description("Kap Haberleri")]
    Kap = 27,

    [Description("Emtia Haberleri")]
    Emtia = 28,

    [Description("Advertorial Haberler")]
    Advertorial = 29,

    [Description("Kobi Kategorisi Haberleri")]
    KobiHaberleri = 30,

    [Description("Bes Haberleri")]
    Bes = 31,

    [Description("Yatırım Sözlüğü")]
    YatirimSozlugu = 32



}