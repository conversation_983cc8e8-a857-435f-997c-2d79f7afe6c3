﻿using Bigpara.Domain;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Bigpara.Core.Application.Common;

public interface IGenericRepository<T,TContext> where T : BaseEntity where TContext : DbContext
{
    IQueryable<T> Query(Expression<Func<T, bool>> filter = null, Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null);

    ICollection<T> GetAll();

    Task<ICollection<T>> GetAllAsync();

    T GetById(int id);

    Task<T> GetByIdAsync(int id);

    T GetByUniqueId(string id);

    Task<T> GetByUniqueIdAsync(string id);

    T Find(Expression<Func<T, bool>> match);

    Task<T> FindAsync(Expression<Func<T, bool>> match);

    ICollection<T> FindAll(Expression<Func<T, bool>> match);

    Task<ICollection<T>> FindAllAsync(Expression<Func<T, bool>> match);
    Task<T> LastOrDefault(Expression<Func<T, bool>> predicate);
    T Add(T entity);

    Task<T> AddAsync(T entity);

    T Update(T updated);

    Task<T> UpdateAsync(T updated);

    void Delete(T t);

    Task<int> DeleteAsync(T t);

    int Count();

    Task<int> CountAsync();

    IEnumerable<T> Filter(
        Expression<Func<T, bool>> filter = null,
        Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null,
        string includeProperties = "",
        int? page = null,
        int? pageSize = null);

    IQueryable<T> FindBy(Expression<Func<T, bool>> predicate);
    IQueryable<T> FindAll();

    bool Exist(Expression<Func<T, bool>> predicate);
}

