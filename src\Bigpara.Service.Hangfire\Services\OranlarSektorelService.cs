﻿using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Domain.Matriks;
using Bigpara.Cache.Redis.Extensions;
using Bigpara.Core.Application.Contracts.Matriks.Borsa;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class OranlarSektorelService : IOranlarSektorelService
{
    private readonly IRedisCacheService _redisCacheService;
    private readonly IOranlarSektorelRepository _oranlarSektorelRepository;
    public OranlarSektorelService
        (
            IOranlarSektorelRepository oranlarSektorelRepository,
            IRedisCacheService redisCacheService
        )
    {
        _oranlarSektorelRepository = oranlarSektorelRepository;
        _redisCacheService = redisCacheService;
    }

    public async Task<int> InsertOranSektorel(OranlarSektorel oranlarSektorel)
    {
        return await _oranlarSektorelRepository.InsertOranSektorel(oranlarSektorel);
    }

    public async Task<List<OranlarSektorel>> GetSektorelMaliOranlar()
    {
        //TODO : Async
        string key = string.Format(CacheKeys.BIGPARA_BORSA_ORAN_SEKTOREL_ALL_KEY);
        return _redisCacheService.GetRedisList(key, () => _oranlarSektorelRepository.GetSektorelMaliOranlar().GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_DURATION).ToList();
    }

    public async Task<int> DeleteOranSektorel()
    {
        return await _oranlarSektorelRepository.DeleteOranSektorel();
    }

    public async virtual Task<List<OranlarSektorel>> GetOranlarSektorels(string sembol)
    {
        //TODO : Async  
        string key = string.Format(CacheKeys.BIGPARA_BORSA_ORAN_SEKTOREL_ALL_SEMBOL, sembol);
        return _redisCacheService.GetRedisList(key, () => _oranlarSektorelRepository.GetOranlarSektorels(sembol).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_DURATION).ToList();
    }

    public async virtual Task<List<OranlarSektorel>> GetOranlarSektorelId(int id)
    {
        //TODO : Async  
        string key = string.Format(CacheKeys.BIGPARA_BORSA_ORAN_SEKTOREL_ALL_SEKTORELID, id);
        return _redisCacheService.GetRedisList(key, () => _oranlarSektorelRepository.GetOranlarSektorelId(id).GetAwaiter().GetResult(), CacheKeys.CACHE_DATA_DURATION).ToList();
    }
}
