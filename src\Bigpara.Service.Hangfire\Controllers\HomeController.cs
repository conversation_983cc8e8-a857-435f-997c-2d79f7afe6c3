using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using Bigpara.Jobs.Hangfire.Models;

namespace Bigpara.Jobs.Hangfire.Controllers;

public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;
    private readonly IConfiguration _configuration;
    private readonly IHttpClientFactory _httpClientFactory;
    public HomeController(ILogger<HomeController> logger, IConfiguration configuration, IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _configuration = configuration;
        _httpClientFactory = httpClientFactory;
    }

    public List<ForeksSymbol> GetCodes(string url)
    {
        var exludeSecurtySembols = new List<string>() { "TE", "F1", "F2", "TF", "TR", "S1", "O" };
        using (HttpClient client = _httpClientFactory.CreateClient())
        {
            var response = client.GetAsync(url).Result;
            if (response.IsSuccessStatusCode)
            {
                var content = response.Content.ReadAsStringAsync().Result;
                var stocks = JsonSerializer.Deserialize<List<ForeksSymbol>>(content);
                return stocks.Where(s => !exludeSecurtySembols.Contains(s.Security)).Where(p => !p.tag.Contains("ReturnIndex")).ToList();

            }
            else
            {
                Console.WriteLine("Hata: " + response.StatusCode);
                return new List<ForeksSymbol>();
            }
        }
    }

    public IActionResult Index()
    {
        var model = new List<ForeksSymbol>();
        var hisseler = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:Hisse:Url"]}");
        //var endeksler = GetCodes("https://feed-definition.foreks.com/symbol/search?domain=BIST&exchange=BIST&marketSector=Index&fields=code,security,securityDesc,securityType,tag&status=ACTIVE");
        //var pariteler = GetCodes("http://feed-definition.foreks.com/symbol/search?fields=code,security,securityDesc,securityType&legacyCode=JPY/TRL&legacyCode=EUR/USD&legacyCode=EUR/GBP&legacyCode=CHF/JPY&legacyCode=GBP/JPY&legacyCode=USD/JPY&legacyCode=EUR/JPY&legacyCode=USD/SAR&legacyCode=EUR/AUD&legacyCode=EUR/CAD&legacyCode=USD/GBP&legacyCode=AUD/JPY&legacyCode=GBP/CAD&legacyCode=CAD/JPY&legacyCode=CAD/CHF&legacyCode=AUD/CHF&legacyCode=AUD/GBP&legacyCode=GBP/AUD&legacyCode=EUR/SAR&legacyCode=AUD/USD&legacyCode=GBP/CHF&legacyCode=EUR/TRL&legacyCode=USD/TRL&legacyCode=XAU/USD&legacyCode=XAG/USD&legacyCode=XAG/EUR&legacyCode=JPY/TRL&legacyCode=CHF/TRL&legacyCode=GBP/TRL&legacyCode=GBP/EUR&legacyCode=EUR/CHF&legacyCode=GBP/USD&legacyCode=XPD/USD&legacyCode=XPT/USD&legacyCode=COPPUS,CFD");
        var pariteler = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:Parite:Url"]}");
        //var viops = GetCodes("https://feed-definition.foreks.com/symbol/search?domain=VIOP&exchange=BIST&status=ACTIVE&fields=code,security,securityDesc,securityType,tag&status=ACTIVE");
        var serbest = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:SerbestPiyasa:Url"]}");
        var tcmb = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:TCMB:Url"]}");


        model.AddRange(pariteler);
        model.AddRange(tcmb);
        model.AddRange(serbest);    
        model.AddRange(hisseler);
        //model.AddRange(endeksler);
        //model.AddRange(viops);

        return View(model);
    }

    public IActionResult IndexRealTime()
    {
        var model = new List<ForeksSymbol>();
        var hisseler = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:Hisse:Url"]}");
        //var endeksler = GetCodes("https://feed-definition.foreks.com/symbol/search?domain=BIST&exchange=BIST&marketSector=Index&fields=code,security,securityDesc,securityType,tag&status=ACTIVE");
        //var pariteler = GetCodes("http://feed-definition.foreks.com/symbol/search?fields=code,security,securityDesc,securityType&legacyCode=JPY/TRL&legacyCode=EUR/USD&legacyCode=EUR/GBP&legacyCode=CHF/JPY&legacyCode=GBP/JPY&legacyCode=USD/JPY&legacyCode=EUR/JPY&legacyCode=USD/SAR&legacyCode=EUR/AUD&legacyCode=EUR/CAD&legacyCode=USD/GBP&legacyCode=AUD/JPY&legacyCode=GBP/CAD&legacyCode=CAD/JPY&legacyCode=CAD/CHF&legacyCode=AUD/CHF&legacyCode=AUD/GBP&legacyCode=GBP/AUD&legacyCode=EUR/SAR&legacyCode=AUD/USD&legacyCode=GBP/CHF&legacyCode=EUR/TRL&legacyCode=USD/TRL&legacyCode=XAU/USD&legacyCode=XAG/USD&legacyCode=XAG/EUR&legacyCode=JPY/TRL&legacyCode=CHF/TRL&legacyCode=GBP/TRL&legacyCode=GBP/EUR&legacyCode=EUR/CHF&legacyCode=GBP/USD&legacyCode=XPD/USD&legacyCode=XPT/USD&legacyCode=COPPUS,CFD");
        var pariteler = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:Parite:Url"]}");
        //var viops = GetCodes("https://feed-definition.foreks.com/symbol/search?domain=VIOP&exchange=BIST&status=ACTIVE&fields=code,security,securityDesc,securityType,tag&status=ACTIVE");
        var serbest = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:SerbestPiyasa:Url"]}");
        var tcmb = GetCodes($"{_configuration["Feed:SymbolApi"]}/?{_configuration["Feed:TCMB:Url"]}");

        model.AddRange(pariteler);
        model.AddRange(hisseler);
        model.AddRange(serbest);
        model.AddRange(tcmb);

        return View(model);
    }

    public IActionResult Privacy()
    {
        return View();
    }

}
