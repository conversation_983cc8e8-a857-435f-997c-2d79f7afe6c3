﻿namespace Bigpara.Domain.Bigpara;

public partial class Headlines
{
    public int Id { get; set; }
    public string Title { get; set; }
    public string SpotTitle { get; set; }
    public string SeoTitle { get; set; }
    public string Picture { get; set; }
    public string SpotPicture { get; set; }
    public string GotoUrl { get; set; }
    public int? ItemTargetId { get; set; }
    public int OrderNumber { get; set; }
    public bool IsActive { get; set; }
    public byte HeadlineType { get; set; }
    public int NewId { get; set; }
}

public partial class HeadlinesAdmin
{
    public int Id { get; set; }
    public string Title { get; set; }
    public string SpotTitle { get; set; }
    public string Picture { get; set; }
    public string SpotPicture { get; set; }
    public string GotoUrl { get; set; }
    public int? ItemTargetId { get; set; }
    public int OrderNumber { get; set; }
    public bool IsActive { get; set; }
    public byte HeadlineType { get; set; }
    public int NewId { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}