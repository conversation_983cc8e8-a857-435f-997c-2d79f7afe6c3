﻿using System.Data;
using System.Net.WebSockets;
using Bigpara.Core.Application.Contracts.Matriks.Borsa;
using Bigpara.Domain;
using Bigpara.Domain.Bigpara;
using Bigpara.Domain.Enums;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.Definitions;
using Bigpara.Domain.Matriks.StoredProcedureResults;
using Bigpara.Domain.SummaryTypes;
using Microsoft.Data.SqlClient;


namespace Bigpara.Persistence.Matriks.Borsa
{
    public class HisseRepository : IHisseRepository
    {
        #region Constants
        private const string ANALIZ_ISLEM_HACMI_BY_ID_KEY = "bigpara.analiz.islemhacmi.id-{0}-{1}-{2}";
        private const string HISSE_BY_ALL_KEY = "bigpara.hisseler.id-{0}";
        #endregion


        private readonly IMatriksDbContext _matriksDbContext;
        public HisseRepository(IMatriksDbContext matriksDbContext)
        {
            _matriksDbContext = matriksDbContext;
        }

        public async Task<Yuzeysel> GetYuzeyselBySembolId(int sembolId)
        {
            throw new NotImplementedException();
        }

        public async Task<List<YuzeyselOpeation>> GetYuzeysellerList()
        {
            return  await _matriksDbContext.ExecuteStoredProcedureAsync<YuzeyselOpeation>("bp.pGetHisseDegerleri");
            //var list = MssqlDatabaseManager.Instance().ExecuteList<YuzeyselOpeation>("bp.pGetHisseDegerleri", SqlConnectionMode.DBMATRIKS);
            //return list ;
        }

        public async Task<List<SembolOzet>> GetHisseDurumByType(ShareProcessStatusType type)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("viewType",(int)type),
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<SembolOzet>("bp.pGetHisseArtanAzalanTLveLotaGore", parameters.ToArray());

            //var results = MssqlDatabaseManager.Instance()
            //    .ExecuteList<SembolOzet>("bp.pGetHisseArtanAzalanTLveLotaGore", parameters, SqlConnectionMode.DBMATRIKS);
            //return results;
        }

        public async Task<List<SembolOzet>> GetHisseDurumByType(ShareProcessStatusType type, SeansTypes seansTypes)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("viewType",(int)type),
            };

            string prodName;

            switch (seansTypes)
            {
                case SeansTypes.Gun:
                    prodName = "bp.pGetHisseArtanAzalanTLveLotaGore";
                    break;
                case SeansTypes.Seans1:
                    prodName = "bp.pGetHisseArtanAzalanTLveLotaGoreSeans1";
                    break;
                case SeansTypes.Seans2:
                    prodName = "bp.pGetHisseArtanAzalanTLveLotaGoreSeans2";
                    break;
                default:
                    prodName = "bp.pGetHisseArtanAzalanTLveLotaGore";
                    break;
            }
            return await _matriksDbContext.ExecuteStoredProcedureAsync<SembolOzet>(prodName, parameters.ToArray());
            //var results = MssqlDatabaseManager.Instance()
            //    .ExecuteList<SembolOzet>(prodName, parameters, SqlConnectionMode.DBMATRIKS);
            //return results ;
        }

        public async Task<List<SembolOzet>> GetAgirlikliOrtalamaDurum(string orderByString, int pageNo, byte pageSize)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("OrderBy", orderByString),
                new SqlParameter("pageNo",pageNo),
                new SqlParameter("pageSize",pageSize),
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<SembolOzet>("bp.pGetYuzeyselHisseAortArtanAzalan", parameters.ToArray());
           // var result = MssqlDatabaseManager.Instance().ExecuteList<SembolOzet>("bp.pGetYuzeyselHisseAortArtanAzalan", parameters, SqlConnectionMode.DBMATRIKS);

           //return result;
        }
        public async Task<PagingResult<SembolOzet>> GetAgirlikliOrtalamaDurumPaging(string orderByString, int pageNo, byte pageSize)
        {
            var outTotalCountParameter = new SqlParameter() { ParameterName = "totalCount", SqlDbType = SqlDbType.Int, Direction = ParameterDirection.Output };
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("OrderBy", orderByString),
                new SqlParameter("pageNo",pageNo),
                new SqlParameter("pageSize",pageSize),
                outTotalCountParameter
            };

            var result = new PagingResult<SembolOzet>();
            result.Data = await _matriksDbContext.ExecuteStoredProcedureAsync<SembolOzet>("bp.pGetYuzeyselHisseAortArtanAzalanByPaging", parameters.ToArray());
            //var result =
            //    mssqlDbManager
            //        .ExecuteList<SembolOzet>("bp.pGetYuzeyselHisseAortArtanAzalanByPaging", parameters, SqlConnectionMode.DBMATRIKS);
            result.TotalCount= (int)(outTotalCountParameter.Value ?? 0);

            return result ;
        }

        public async Task<List<SembolOzet>> GetAgirlikliOrtalamaDurumV2(string orderByString)
        {
            //var mssqlDbManager = MssqlDatabaseManager.Instance();

            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("OrderBy", orderByString),
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<SembolOzet>("bp.pGetYuzeyselHisseAortArtanAzalanV2", parameters.ToArray());
            //var result = mssqlDbManager.ExecuteList<SembolOzet>("bp.pGetYuzeyselHisseAortArtanAzalanByPagingV2", parameters, SqlConnectionMode.DBMATRIKS);

            //return result;
        }

        public async Task<List<HisseYuzeysel>> GetHisseYuzeysel()
        {
            return await  _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetHisseYuzeyselDegerleri");
            //var result = MssqlDatabaseManager.Instance().ExecuteList<HisseYuzeysel>("bp.pGetHisseYuzeyselDegerleri", SqlConnectionMode.DBMATRIKS);
        
            //return result ;
        }

        public async Task<HisseYuzeysel> GetHisseYuzeyselDegerleriBySembolId(int sembolId)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("sembolId", sembolId),
            };

            return await _matriksDbContext.ExecuteStoredProcedureSingleAsync<HisseYuzeysel>("bp.pGetHisseYuzeyselDegerleriBySembol", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteSingle<HisseYuzeysel>("bp.pGetHisseYuzeyselDegerleriBySembol",parameters, SqlConnectionMode.DBMATRIKS);
        
            //return result ;
        }

        
        public async Task<List<HisseYuzeyselOnline>> GetHisseYuzeyselOnline()
        {
            return await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeyselOnline>("bp.pGetHisseYuzeyselOnlineDegerleri");
            //var result = MssqlDatabaseManager.Instance().ExecuteList<HisseYuzeyselOnline>("bp.pGetHisseYuzeyselOnlineDegerleri", SqlConnectionMode.DBMATRIKS);

            //return result ?? new List<HisseYuzeyselOnline>();
        }


        public async Task<List<HisseYuzeyselIndikator>> GetHisseYuzeyselTarihsiz(string sembol)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("sembol", sembol),
                
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeyselIndikator>("bp.pGetHisseYuzeyselTarihsizDegerleri", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteList<HisseYuzeyselIndikator>("bp.pGetHisseYuzeyselTarihsizDegerleri", parameters, SqlConnectionMode.DBMATRIKS);
            //return result ?? new List<HisseYuzeyselIndikator>();
        }
        public async Task<List<HisseYuzeysel>> GetHisseYuzeyselTarihsizBySembolId(int sembol)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("sembolId", sembol),
                
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetHisseYuzeyselTarihsizDegerleriBySembolId", parameters.ToArray());

            //var result = MssqlDatabaseManager.Instance().ExecuteList<HisseYuzeysel>("bp.pGetHisseYuzeyselTarihsizDegerleriBySembolId", parameters, SqlConnectionMode.DBMATRIKS);
            //return result;
        }

        public async Task<List<Hisse>> GetHisseList()
        {
            return await _matriksDbContext.ExecuteStoredProcedureAsync<Hisse>("bp.pGetHisseListesi");
            //var result = MssqlDatabaseManager.Instance().ExecuteList<Hisse>("bp.pGetHisseListesi", SqlConnectionMode.DBMATRIKS);
            //return result ;
        }

        public async Task<List<Hisse>> GetTumSemboller()
        {
            return await _matriksDbContext.ExecuteStoredProcedureAsync<Hisse>("bp.GetTumSemboller");
            //var result = MssqlDatabaseManager.Instance().ExecuteList<Hisse>("bp.GetTumSemboller", SqlConnectionMode.DBMATRIKS);
            //return result;
        }

        public async Task<List<DunyaBorsalari>> GetDunyaBorsalariData()
        {
            return await _matriksDbContext.ExecuteStoredProcedureAsync<DunyaBorsalari>("bp.pGetDunyaBorsalari");
            //var result = MssqlDatabaseManager.Instance().ExecuteList<DunyaBorsalari>("bp.pGetDunyaBorsalari", SqlConnectionMode.DBMATRIKS);
            //return result ;
        }

        public async Task<List<BilancoDonem>> GetBilancoDonems(string sembol, string donem, int cins)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("sembol", sembol),
                new SqlParameter("donem", donem),
                new SqlParameter("cins", cins),
            };
            return await _matriksDbContext.ExecuteStoredProcedureAsync<BilancoDonem>("bp.pGetBilancoDonemData", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteList<BilancoDonem>("bp.pGetBilancoDonemData", parameters, SqlConnectionMode.DBMATRIKS);
            //return result;
        }
        public async Task<List<BilancoDonem>> GetBilancoDonemByYil(string sembol, string donem, int cins, int yil)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("sembol", sembol),
                new SqlParameter("donem", donem),
                new SqlParameter("cins", cins),
                 new SqlParameter("yil", yil),
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<BilancoDonem>("bp.pGetBilancoDonemDataByYil", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteList<BilancoDonem>("bp.pGetBilancoDonemDataByYil", parameters, SqlConnectionMode.DBMATRIKS);
            //return result;
        }




        public async Task<List<BilancoDonemSembol>> GetBilancoDonemsBySembol(string sembol)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("sembol", sembol),
         
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<BilancoDonemSembol>("bp.pGetBilancoDonemByHisseKod", parameters.ToArray());
           
            //var result = MssqlDatabaseManager.Instance().ExecuteList<BilancoDonemSembol>("bp.pGetBilancoDonemByHisseKod", parameters, SqlConnectionMode.DBMATRIKS);
            //return result;
        }

        public async Task<PagingResult<HisseYuzeysel>> GetEndeksHisseYuzeysels(string endeks, string hisse, int pageNo, byte pageSize, char? letter)
        {
            var outTotalCountParameter = new SqlParameter() { ParameterName = "totalCount", SqlDbType = SqlDbType.Int, Direction = ParameterDirection.Output };
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("endeks",string.IsNullOrEmpty(endeks)? DBNull.Value:endeks),
                new SqlParameter("sembol",string.IsNullOrEmpty(hisse)? DBNull.Value:hisse),
                new SqlParameter("pageNo",pageNo),
                new SqlParameter("pageSize", pageSize),
                new SqlParameter("letter",letter.HasValue?letter.Value: DBNull.Value),
                outTotalCountParameter
            };

            var result = new PagingResult<HisseYuzeysel>();

            result.Data= await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetEndeksHisseFiyatlari", parameters.ToArray());
            // Örnek kullanım: output parametresinden değer okurken null kontrolü eklenmiş hali
            result.TotalCount = outTotalCountParameter.Value != null && outTotalCountParameter.Value != DBNull.Value
                ? Convert.ToInt32(outTotalCountParameter.Value)
                : 0;

            return result;

        }

        public async Task<List<HisseYuzeysel>> GetEndeksHisseYuzeysels(string endeks)
        {

 
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("endeks",string.IsNullOrEmpty(endeks)? DBNull.Value:endeks),
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetEndeksHisseFiyatlariV2", parameters.ToArray());

            //var result = MssqlDatabaseManager.Instance().ExecuteList<HisseYuzeysel>("bp.pGetEndeksHisseFiyatlariV2", parameters, SqlConnectionMode.DBMATRIKS);

            //return result;
        }


        public async Task<PagingResult<HisseYuzeysel>> GetHisseFiyatlariByEndeksVeHarf(string endeks, int pageNo, byte pageSize, char? letter)
        {
            var outTotalCountParameter = new SqlParameter() { ParameterName = "totalCount", SqlDbType = SqlDbType.Int, Direction = ParameterDirection.Output };

            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("endeks",string.IsNullOrEmpty(endeks) || endeks.ToLower().Equals("tum")  || endeks.ToLower().Equals("tum-hisseler") ? DBNull.Value:endeks),
                new SqlParameter("pageNo",pageNo),
                new SqlParameter("pageSize", pageSize),
                new SqlParameter("letter",letter.HasValue?letter:DBNull.Value),
                outTotalCountParameter
            };
            var result = new PagingResult<HisseYuzeysel>();

            result.Data = await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetHisseFiyatlariByEndeksVeHarf", parameters.ToArray());
            //var mssqlDbManager = MssqlDatabaseManager.Instance();
            //var result = mssqlDbManager.ExecuteList<HisseYuzeysel>("bp.pGetHisseFiyatlariByEndeksVeHarf", parameters, SqlConnectionMode.DBMATRIKS);

            result.TotalCount = outTotalCountParameter.Value != null && outTotalCountParameter.Value != DBNull.Value
            ? Convert.ToInt32(outTotalCountParameter.Value)
            : 0;

            return result;
        }



        public async Task<PagingResult<HisseYuzeysel>> GetHisseFiyatlariByEndeksVeHarfOnline(string endeks, int pageNo, byte pageSize, char? letter)
        {
            var outTotalCountParameter = new SqlParameter() { ParameterName = "totalCount", SqlDbType = SqlDbType.Int, Direction = ParameterDirection.Output };

            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("endeks",string.IsNullOrEmpty(endeks) || endeks.ToLower().Equals("tum")  || endeks.ToLower().Equals("tum-hisseler") ? DBNull.Value:endeks),
                new SqlParameter("pageNo",pageNo),
                new SqlParameter("pageSize", pageSize),
                new SqlParameter("letter",letter.HasValue?letter:DBNull.Value),
               outTotalCountParameter
            };
            var result = new PagingResult<HisseYuzeysel>();

            result.Data= await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetHisseFiyatlariByEndeksVeHarfOnline", parameters.ToArray());
            result.TotalCount = outTotalCountParameter.Value != null && outTotalCountParameter.Value != DBNull.Value
                   ? Convert.ToInt32(outTotalCountParameter.Value)
                   : 0;
            return result;
        }

        public async Task<PagingResult<PerformansAnaliz>> GetPerformansAnaliz(string endeks, int sembolId, DateTime? startDateTime, DateTime? endDateTime, string currency, int orderBy, int pageNo, byte pageSize)
        {
            var outTotalCountParameter = new SqlParameter() { ParameterName = "totalCount", SqlDbType = SqlDbType.Int, Direction = ParameterDirection.Output };


            var parameters = new List<SqlParameter>()
            {
                //new SqlParameter("endeks",string.IsNullOrEmpty(endeks) || endeks.ToLower().Equals("tum") ? (object)DBNull.Value:endeks),
                //new SqlParameter("sembolId",sembolId<=0 ? (object)DBNull.Value:sembolId),
                new SqlParameter("dateBegin",startDateTime),
                new SqlParameter("dateEnd",endDateTime),
                new SqlParameter("Kur",currency),
                new SqlParameter("orderBy",orderBy),
                new SqlParameter("pageNo",pageNo),
                new SqlParameter("pageSize", pageSize),              
                outTotalCountParameter
            };
            if (!string.IsNullOrEmpty(endeks) && !endeks.ToLower().Equals("tum"))
            {
                parameters.Add(new SqlParameter("endeks", endeks));
            }
            if (sembolId > 0)
            {
                parameters.Add(new SqlParameter("sembolId", sembolId));
            }
            var result = new PagingResult<PerformansAnaliz>();
           
            result.Data= await _matriksDbContext.ExecuteStoredProcedureAsync<PerformansAnaliz>("bp.pGetPerformansAnaliz", parameters.ToArray());
            result.TotalCount = outTotalCountParameter.Value != null && outTotalCountParameter.Value != DBNull.Value
             ? Convert.ToInt32(outTotalCountParameter.Value)
             : 0;

            return result;
        }

        public async Task<List<PerformansAnaliz>> GetPerformansAnaliz(string endeks, int sembolId, DateTime? startDateTime, DateTime? endDateTime, string currency, int orderBy)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("dateBegin",startDateTime),
                new SqlParameter("dateEnd",endDateTime),
                new SqlParameter("Kur",currency),
                new SqlParameter("orderBy",orderBy),
            };
            if (!string.IsNullOrEmpty(endeks) && !endeks.ToLower().Equals("tum"))
            {
                parameters.Add(new SqlParameter("endeks", endeks));
            }
            if (sembolId > 0)
            {
                parameters.Add(new SqlParameter("sembolId", sembolId));
            }

            return await _matriksDbContext.ExecuteStoredProcedureAsync<PerformansAnaliz>("bp.pGetPerformansAnalizV2", parameters.ToArray());
            //var mssqlDbManager = MssqlDatabaseManager.Instance();
            //var result = mssqlDbManager.ExecuteList<PerformansAnaliz>("bp.pGetPerformansAnalizV2", parameters, SqlConnectionMode.DBMATRIKS);


            //return result ;
        }


        public async Task<List<HisseYuzeysel>> GetUserHisseYuzeysel(int userId)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("userId", userId),                
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetUserHisseYuzeyselTarihsizDegerleri", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteList<HisseYuzeysel>("bp.pGetUserHisseYuzeyselTarihsizDegerleri", parameters, SqlConnectionMode.DBMATRIKS);

            //return result;
        }

        public async Task<List<HisseYuzeysel>> GetUserHisseYuzeyselOnline(int userId)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("userId", userId),                
            };
            return await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetUserHisseYuzeyselTarihsizDegerleriOnline", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteList<HisseYuzeysel>("bp.pGetUserHisseYuzeyselTarihsizDegerleriOnline", parameters, SqlConnectionMode.DBMATRIKS);
            //return result;
        }

        public async Task<List<KisaTanimSemboller>> GetDefaultPiyasaBandi()
        {
            return await _matriksDbContext.ExecuteStoredProcedureAsync<KisaTanimSemboller>("bp.pGePiyasaBandiSifirla");
            //var result = MssqlDatabaseManager.Instance().ExecuteList<KisaTanimSemboller>("bp.[pGePiyasaBandiSifirla]", SqlConnectionMode.DBMATRIKS);
            //return result;
        }
        public async Task<List<KisaTanimSemboller>> GetUserHisseSembolList(int userId, int pageType)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("userId", userId),
              
            };
            if (pageType != (int)PageType.Default)
            {
                parameters.Add(new SqlParameter("pageType", pageType));
            }

            return await _matriksDbContext.ExecuteStoredProcedureAsync<KisaTanimSemboller>("bp.pGetUserHisseSemboller", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteList<KisaTanimSemboller>("bp.pGetUserHisseSemboller", parameters, SqlConnectionMode.DBMATRIKS);
            //return result ;
        }
        public async Task<List<Haberler>> GetUserHisseHaberleri(int userId)
        {
       //     var watch = new Stopwatch();

            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("userId", userId),
                
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<Haberler>("bp.pGetUserHisseKabHaberleri", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteList<Haberler>("bp.pGetUserHisseKabHaberleri", parameters, SqlConnectionMode.DBMATRIKS);
            ////watch.Stop();
            ////double elapsedMs = watch.ElapsedMilliseconds;
            ////LogHelper.Error("GetUserHisseHaberleri : Duration:" + elapsedMs);

            //return result ;
        }

        public async Task<List<UserHisseHaber>> GetUserSembolHaberleri(int userId)
        {
          //  var watch = new Stopwatch();

            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("userId", userId),
                
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<UserHisseHaber>("pGetUserHisseHaberleri", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteList<UserHisseHaber>("pGetUserHisseHaberleri", parameters, SqlConnectionMode.BIGPARA);
            ////watch.Stop();
            ////double elapsedMs = watch.ElapsedMilliseconds;
            ////LogHelper.Error("pGetUserHisseHaberleri : Duration:" + elapsedMs);

            //return result;
        }


        public async Task<List<UserSembol>> GetUserHisseList(int userId)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("userId", userId),
              
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<UserSembol>("spGetUserHisseList", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteList<UserSembol>("spGetUserHisseList", parameters, SqlConnectionMode.BIGPARA);
            //return result;
        }

        public async Task<List<LiveStockHisse>> GetYuzeselOnlineBySembol(DateTime? tarih)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("tarih", tarih.HasValue?tarih:DBNull.Value),
                
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<LiveStockHisse>("bp.pGetYuzeselOnlineBySembolV2", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance().ExecuteList<LiveStockHisse>("bp.pGetYuzeselOnlineBySembolV2", parameters, SqlConnectionMode.DBMATRIKS);
            //return result;
        }

        /// <summary>
        /// 1: 
        /// </summary>
        /// <param name="orderId"></param>
        /// <param name="orderByDirection"></param>
        /// <param name="pageNo"></param>
        /// <param name="pageSize"></param>
        /// <param name="totalCount"></param>
        /// <returns></returns>
        public async Task<List<HisseYuzeysel>> GetHisselerIslemHacmiArtanAzalan(OrderByDirection orderByDirection)
        {

            var parameters = new List<SqlParameter>()
                {
                    new SqlParameter("orderBy", (short) orderByDirection),
                };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetHisselerIslemHacmiArtanAzalan", parameters.ToArray());
            //var mssqlDbManager = MssqlDatabaseManager.Instance();
            //var result = mssqlDbManager.ExecuteList<HisseYuzeysel>("bp.pGetHisselerIslemHacmiArtanAzalan",
            //    parameters, SqlConnectionMode.DBMATRIKS);
            //return result ;
        }




        public async Task<int> SaveUserHisse(int userId, int sembolId, int pageType, int opId)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("userId",userId),
                new SqlParameter("sembolId",sembolId),
                new SqlParameter("pageType", pageType),
                new SqlParameter("opId",opId)
            };

            return await _matriksDbContext.ExecuteNonQueryAsync("dbo.spCreateOrUpdateUserSembol", parameters.ToArray());

            //var mssqlDbManager = MssqlDatabaseManager.Instance();
            //var result = mssqlDbManager.ExecuteNonQuery("dbo.spCreateOrUpdateUserSembol", parameters, SqlConnectionMode.BIGPARA);

            //return result;
        }


        public async Task<int> SaveUserHisse(int userId, int sembolId, int pageType, int opId, int orderId)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("userId",userId),
                new SqlParameter("sembolId",sembolId),
                new SqlParameter("pageType", pageType),
                new SqlParameter("opId",opId),
                new SqlParameter("orderId",@orderId)
 
            };

            return await _matriksDbContext.ExecuteNonQueryAsync("dbo.spCreateOrUpdateUserSembolV2", parameters.ToArray());

        }


        public async Task<PagingResult<HisseYuzeysel>> GetHisselerIslemHacmiFiyatArtanAzalan(TradingVolumeFilterType tradingVolumeFilterType, int pageNo, byte pageSize)
        {
            var outTotalCountParameter = new SqlParameter() { ParameterName = "totalCount", SqlDbType = SqlDbType.Int, Direction = ParameterDirection.Output };

            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("islemTipi", (short)tradingVolumeFilterType),
                new SqlParameter("pageNo",pageNo),
                new SqlParameter("pageSize", pageSize),
                outTotalCountParameter
            };

            var result = new PagingResult<HisseYuzeysel>();
            result.Data= _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetHisseIslemHacmiFiyatArtanAzalan", parameters.ToArray()).Result;
            result.TotalCount = outTotalCountParameter.Value != null && outTotalCountParameter.Value != DBNull.Value ? Convert.ToInt32(outTotalCountParameter.Value) : 0;
            return result;
        }
        public async Task<List<HisseYuzeysel>> GetHisselerIslemHacmiFiyatArtanAzalan(TradingVolumeFilterType tradingVolumeFilterType)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("islemTipi", (short)tradingVolumeFilterType),

            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<HisseYuzeysel>("bp.pGetHisseIslemHacmiFiyatArtanAzalanV2", parameters.ToArray());
            //var mssqlDbManager = MssqlDatabaseManager.Instance();
            //var result = mssqlDbManager.ExecuteList<HisseYuzeysel>("bp.pGetHisseIslemHacmiFiyatArtanAzalanV2", parameters, SqlConnectionMode.DBMATRIKS);
            //return result;
        }



        public async Task<List<KisaTanimSemboller>> GetPiyasaBandiHisseListe()
        {
            return await _matriksDbContext.ExecuteStoredProcedureAsync<KisaTanimSemboller>("bp.pGetPiyasaBandiHisseListe");
            //var mssqlDbManager = MssqlDatabaseManager.Instance();
            //var result = mssqlDbManager.ExecuteList<KisaTanimSemboller>("bp.pGetPiyasaBandiHisseListe", SqlConnectionMode.DBMATRIKS);
            //return result;
        }

        public async Task<int> SaveUserAlert(int userId, string sembol, double price, string alertField)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("userId",userId),
                new SqlParameter("sembol",sembol),
                new SqlParameter("price", price),
                new SqlParameter("alertField", alertField),
            };

            return await _matriksDbContext.ExecuteNonQueryAsync("bp.pCreateUserSembolAlertV2", parameters.ToArray());

        }

        public async Task<List<SembolOzet>> GetAgirlikliOrtalama(string orderBy, int pageNumber, int pageSize)
        {
            var parameters = new List<SqlParameter>()
            {
                new SqlParameter("OrderBy", orderBy),
                new SqlParameter("pageNo", pageNumber),
                new SqlParameter("pageSize", pageSize)
            };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<SembolOzet>("[bp].[pGetYuzeyselHisseAortArtanAzalan]", parameters.ToArray());
            //var result = MssqlDatabaseManager.Instance()
            //    .ExecuteList<SembolOzet>("[bp].[pGetYuzeyselHisseAortArtanAzalan]", parameters,
            //        SqlConnectionMode.DBMATRIKS);
            //return result;
        }

        public async Task<int> DeleteUserAlert (int userId, int sembolId)
        {
            var parameters = new List<SqlParameter>()
                {
                    new SqlParameter("userId", userId),
                    new SqlParameter("sembolId", sembolId),
                };

            return await _matriksDbContext.ExecuteNonQueryAsync("bp.pDeleteUserSembolAlert", parameters.ToArray());
           
        }

        public async Task<List<UserAlert>> GetUserAlerts(int userId)
        {
            var parameters = new List<SqlParameter>()
                {
                    new SqlParameter("userId", userId),

                };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<UserAlert>("bp.pGetUserSembolAlertList", parameters.ToArray());
           
        }


        public async Task<List<UserAlert>> GetUserAlertResult(int userId)
        {
            var parameters = new List<SqlParameter>()
                {
                    new SqlParameter("userId", userId),

                };

            return await _matriksDbContext.ExecuteStoredProcedureAsync<UserAlert>("bp.pGetUserSembolAlertResult", parameters.ToArray());

        }


        public async Task<int> UserAlertDeactivate(int userId, int id)
        {
            var parameters = new List<SqlParameter>()
                {
                    new SqlParameter("userId", userId),
                    new SqlParameter("id", id),
                };

            return await _matriksDbContext.ExecuteNonQueryAsync("pUserSembolAlertDeactivate", parameters.ToArray());

        }

        public async Task<int> UserAlertInstanceAlert(int userId)
        {
            var parameters = new List<SqlParameter>(){
                    new SqlParameter("userId", userId),
             };


            return await _matriksDbContext.ExecuteNonQueryAsync("bp.pCreateUserInstanceAlert", parameters.ToArray());

        }

    }
}

