﻿using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Core.Application.Contracts.Matriks.OtomatikTeknikYorum;
using Bigpara.Domain.Matriks;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class OtomatikTeknikYorumlarService : IOtomatikTeknikYorumlarService
{
    private readonly ICacheService _cacheService;
    private readonly IOtomatikTeknikYorumlarRepository _otomatikTeknikYorumlarRepository;

    public OtomatikTeknikYorumlarService
    (
        ICacheService cacheService,
        IOtomatikTeknikYorumlarRepository otomatikTeknikYorumlarRepository
    )
    {
        _otomatikTeknikYorumlarRepository = otomatikTeknikYorumlarRepository;
        _cacheService = cacheService;
    }

    public async Task<List<OtomatikTeknikYorumlar>> GetOtomatikTeknikYorumlars(string sembol, DateTime dateTime)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_OTOMATIK_TEKNIK_YORUM_BY_SEMBOL_TARIH, sembol, dateTime.ToString("yyyyMMdd"));
        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_DURATION, () => _otomatikTeknikYorumlarRepository.GetOtomatikTeknikYorumlars(sembol, dateTime))).ToList();
    }

    public async Task<int> CreateOrUpdateOtomatikTeknikYorumlar(OtomatikTeknikYorumlar otomatikTeknikYorumlar)
    {
        return await _otomatikTeknikYorumlarRepository.CreateOrUpdateOtomatikTeknikYorumlar(otomatikTeknikYorumlar);
    }
    public async Task<List<OtomatikTeknikYorumlar>> GetOtomatikTeknikYorumlars(string sembol)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_OTOMATIK_TEKNIK_YORUM_BY_SEMBOL, sembol);

        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_10DK_DURATION, () => _otomatikTeknikYorumlarRepository.GetOtomatikTeknikYorumlars(sembol))).ToList();
    }
}
