﻿using Bigpara.Cache.Interfaces;
using Bigpara.Cache.Memory.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Bigpara.Cache.Memory.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddCustomMemoryCache(this IServiceCollection services)
    {
        services.AddMemoryCache();
        services.AddScoped<ICacheService, MemoryCacheService>();

        return services;
    }
}
