﻿namespace Bigpara.Cache;

public class CacheKeys
{
    #region Constants Realtime
    public const string BIGPARA_LIVESTOCK_LASTUPDATE = "realtime:live.stock.lastupdate";//NewsType, NewsId
    #endregion

    #region Constants Haber
    private const string _redisNewsDetailKey = "bigpara:news.detail.by.type.id.viewmodel.{0}-{1}";//NewsType, NewsId
    private const string _redisMostReadNewsKey = "bigpara:istatistik.most.read.news";
    private const string _redisMostClickedStock = "bigpara:istatistik.most.click.stock";
    private const string _redisallnewsList = "bigpara:news.hepsi.liste";
    #endregion

    #region Constants Sembol Service
    public const string BIGPARA_BORSA_MATRIKS_ENDEKS_ALL_KEY = "bigpara:borsa.matriks.endeks.tanimlar.list.all";
    public const string BIGPARA_BORSA_MATRIKS_SEMBOL_ENDEKS_ALL_KEY_BY_SEMBOL = "bigpara:borsa.matriks.sembol.endeks.tanimlar.list.all.by.endeks.{0}";
    public const string BIGPARA_BORSA_MATRIKS_SEMBOL_TANIM_DETAY_KEY_BY_SEMBOLID = "bigpara:borsa.matriks.sembol.tanimlar.list.all.by.sembolid.{0}";
    public const string BIGPARA_BORSA_MATRIKS_SEMBOL_KISA_TANIM_DETAY_KEY_BY_SEMBOL = "bigpara:borsa.matriks.sembol.tanimlar.summary.all.by.sembol.{0}";
    public const string BIGPARA_BORSA_MATRIKS_SEMBOL_ENDEKS_ALL_KEY = "bigpara:borsa.matriks.sembol.endeks.tanimlar.list.all";
    public const string BIGPARA_BORSA_MATRIKS_SEMBOL_TANIM_DETAY_KEY_BY_PIYASAID = "bigpara:borsa.matriks.sembol.tanimlar.list.all.by.piyasaid.{0}";
    public const string BIGPARA_BORSA_MATRIKS_ENDEKS_ALL_KEY_BY_ENDEKS_SEMBOL = "bigpara:borsa.matriks.endeks.tanimlar.list.all.by.endeks.{0}";
    public const string BIGPARA_BORSA_MATRIKS_SEMBOL_READ_COUNTS_SUMMARY = "bigpara:borsa.matriks.sembol.read.count.summary.all";
    public const string BIGPARA_BORSA_MATRIKS_SEMBOLLEREK_BY_SEMBOLID = "bigpara:borsa.matriks.sembollerek.by.sembolid.{0}";
    #endregion

    #region Constants Akaryakit Service
    public const string BIGPARA_AKARYAKIT_LIST_BY_DATE = "bigpara:akaryakit.list.{0}";
    #endregion

    #region Constants altin service
    public const string BIGPARA_ALTIN_LIST_BY_WIDGETID = "bigpara:altin.list.by.widgetid.{0}";
    public const string BIGPARA_ALTIN_SERBEST_PIYASA_LIST_BY_WIDGETID = "bigpara:altin.list.by.widgetid.{0}";
    public const string BIGPARA_ALTIN_SERBEST_PIYASA_LIST_BY_WIDGETID_BY_DATE = "bigpara:altin.list.by.widgetid.{0}.{1}";
    public const string BIGPARA_GUMUS_SERBEST_PIYASA_LIST_BY_WIDGETID = "bigpara:gumus.list.by.widgetid.{0}";
    #endregion

    #region Constants Haber
    public const string NEWS_TOP_ALL = "bigpara:news.top";
    public const string NEWS_TOP_BY_CATEGORYIDS = "bigpara:news.top.by.categoryids.{0}";
    public const string NEWS_TOP_BY_CATEGORYID = "bigpara:news.top.by.categoryid.{0}";
    public const string NEWS_BY_CATEGORY_IDS = "bigpara:news.by.categoryids.{0}";
    public const string NEWS_BY_CATEGORY_ID = "bigpara:news.by.categoryid.{0}";
    public const string NEWS_TECHNOLOGY_BY_CATEGORY_ID = "bigpara:news.technology.top.by.categoryids.{0}";
    public const string NEWS_KOBI_BY_CATEGORY_ID = "bigpara:news.kobi.top.by.categoryids.{0}";
    public const string NEWS_BREAK_BY_CATEGORY_ID = "bigpara:news.break.top.by.categoryids.{0}";
    public const string NEWS_MARKET_BY_CATEGORY_ID = "bigpara:news.break.top.by.categoryids.{0}";
    public const string NEWS_LATEST_SUB_HEAD_BY_CATEGORY_ID = "bigpara:news.latest.subhead.top.by.categoryids.{0}";
    public const string NEWS_DETAIL_BY_TYPE_ID = "bigpara:news.detail.by.type.id.News-{0}"; //news type 1
    public const string NEWS_PREVIOUS_NEXT_ID_DIRECTION = "bigpara:news.detail.by.id.photo.direction.{0}-{1}-{2}";
    public const string NEWS_LIST_BETWEEN_DATE_BY_SORTDER_DIRECTION = "bigpara:news.list.by.id.sort.direction.{0}-{1}-{2}-{3}-{4}-{5}";
    public const string NEWS_MOST_READ_BY_PERIOD = "bigpara:news.most.read.by.period.{0}";
    public const string NEWS_PHOTO_GALLERY_BY_ID = "bigpara:news.photo.gallery.by.period.{0}";
    public const string NEWS_LATEST_PHOTO_NEWS_BY_TOP = "bigpara:news.latest.photo.news.by.top.{0}";
    public const string NEWS_SEMBOL_BY_ID = "bigpara:news.sembol.by.sembol.{0}";
    public const string NEWS_LATEST_BY_TOP = "bigpara:news.latest.by.top.{0}";
    public const string NEWS_LATEST_BY_CATEGORY_ID_TOP = "bigpara:news.latest.by.categoryId.top.{0}.{1}";
    public const string NEWS_READCOUNT_BY_ID = "bigpara:news.read.count.by.id.{0}";
    public const string SUB_HEADLINES_NEWS = "bigpara:news.sub.headlines";
    public const string NEWS_WORLD = "bigpara:news.world";
    #endregion

    #region Constants Bigpara Uzmanlari
    public const string BIGPARA_UZMANLARI_YAZILARI_READCOUNT = "bigpara:bigparauzmanlari.yazilari.readcount";
    public const string BIGPARA_UZMANLARI_YAZILARI_SON_TOPN_YAZI_READCOUNT = "bigpara:bigparauzmanlari.yazilari.sontopn_yazi.readcount";
    public const string BIGPARA_UZMANLARI_YAZARLARI_HEPSI = "bigpara:bigpara.uzmanlari.yazarlari.hepsi";
    public const string BIGPARA_UZMANLARI_YAZAR_BY_ID = "bigpara:bigpara.uzmanlari.yazardetay.by.id.{0}";
    public const string BIGPARA_UZMANLARI_YAZAR_YAZISI_BY_ID = "bigpara:bigpara.uzmanlari.yazi.detay.by.id.{0}";
    public const string BIGPARA_UZMANLARI_BY_TYPE = "bigpara:bigpara.uzmanlari.by.type.{0}";
    public const string BIGPARA_UZMANLARI_ARTICLE_BY_TYPE_TOP = "bigpara:bigpara.uzmanlari.article.by.type.{0}.top.{1}";
    public const string BIGPARA_UZMANLARI_ARTICLE_BY_AUTHORIDS = "bigpara:bigpara.uzmanlari.article.by.author.ids.{0}";
    public const string BIGPARA_UZMANLARI_YAZILARI_BY_AUTHOR_TOP = "bigpara:bigpara.uzmanlari.yazilari.by.yazar.id.{0}.top.{1}";
    public const string BIGPARA_UZMANLARI_YAZILARI_ORDERED_BY_TYPE_TOP = "bigpara:bigpara.uzmanlari.yazilari.sirali.by.type.{0}";
    public const string BIGPARA_UZMANLARI_SORULAR_BY_YAZAR_ID = "bigpara:bigpara.uzmanlari.sorular.by.yazar.id.{0}";
    public const string BIGPARA_UZMANLARI_SORULAR_BY_KATEGORI_ID = "bigpara:bigpara.uzmanlari.sorular.by.kategori.id.{0}";
    public const string BIGPARA_UZMANLARI_SORULAR_ARAMA_BY_TEXT = "bigpara:bigpara.uzmanlari.sorular.arama.by.text.{0}";
    public const string BIGPARA_UZMANLARI_SORU_KATEGORILER_BY_YAZAR_ID = "bigpara:bigpara.uzmanlari.soru.kategorileri.by.yazar.id.{0}";
    public const string BIGPARA_UZMANLARI_CEVAP_BY_SORU_ID = "bigpara:bigpara.uzmanlari.cevap.by.soru.id.{0}";
    #endregion

    #region Constants Araci Kurumlar
    public const string ARACIKURUM_GORUSLERI_BY_TOP = "bigpara:aracikurum.gorusleri.top.{0}";
    public const string ARACIKURUM_GORUSLERI_BY_TYPE_TOP = "bigpara:aracikurum.gorusleri.by.type.top.{0}.{1}";
    public const string ARACIKURUM_GORUSLERI_DETAY = "bigpara:aracikurum.gorusleri.detay.{0}";
    #endregion

    #region Constants BistUyeleriService
    public const string BIST_UYELERI_BY_TYPE = "bigpara:bist.uyeleri.by.type.{0}";
    #endregion

    #region Constants Meta Data Service
    public const string META_DATA_KEY = "bigpara:metadata-{0}";
    public const string META_DATA_KEY_BY_ID = "bigpara:metadata-{0}-{1}";
    #endregion

    #region Constants Manset
    public const string NEWS_HEADLINES_BY_GRUPID_TOP = "bigpara:news.headline.top.by.grupid.{0}-{1}";
    public const string AMVG_HEADLINES_BY_GRUPID_TOP = "bigpara:amvg.headline.top.by.grupid.{0}-{1}";
    #endregion

    #region Constants Radikal Service
    public const string BIGPARA_RADIKAL_HABERDETAY = "bigpara:radikal.haberdetay";
    #endregion

    #region Client AuthenticationService
    public const string CLIENT_AUTHENTICATION_SERVICE_KEY = "bigpara:services.client.userName.{0}.password.{1}";
    #endregion

    #region Constants SektorRaporlari
    public const string BIGPARA_SEKTOR_REPORT_BY_MONTH = "bigpara:report.sektor.by.month.{0}.{1}.{2}";
    #endregion

    public const string BANKBRANCH_ALL_DATA = "bigpara:bankbranch.all";

    #region Constants Video
    public const string BIGPARA_VIDEO_BY_CATEGORYIDS = "bigpara:video.by.categoryids.{0}";
    public const string BIGPARA_VIDEO_BY_TOP = "bigpara:video.by.top.{0}";
    public const string BIGPARA_TOP_VIDEO_BY_CATEGORYIDS = "bigpara:top.{0}.video.by.categoryids.{1}";
    public const string BIGPARA_VIDEO_BY_ID = "bigpara:video.detail.by.id.{0}";
    public const string BIGPARA_VIDEO_BY_OLDID = "bigpara:video.detail.by.oldid.{0}";
    public const string BIGPARA_VIDEO_BY_PAGE = "bigpara:video.list.by.paging.{0}.{1}";
    public const string BIGPARA_VIDEO_BY_PAGE_ALL = "bigpara:video.list.by.paging.all.{0}.{1}";
    #endregion

    #region Constants Seans Raporlari
    public const string BIGPARA_BORSA_SEANS_RAPORU_ALL_KEY = "bigpara:borsa.matriks.seans.raporu.hepsi";
    public const string BIGPARA_BORSA_SEANS_RAPORU_TOPN_KEY = "bigpara:borsa.matriks.seans.raporu.top{0}";
    #endregion

    #region Constants Borsa Service
    public const string SEMBOL_BY_ID_KEY = "bigpara:sembol.id-{0}";
    public const string HISSE_BY_ALL_KEY = "bigpara:hisse.semboller.hepsi.list";
    public const string SEMBOLLER_BY_ALL_KEY = "bigpara:semboller.hepsi.tum.list";
    public const string ANALIZ_ISLEM_HACMI_BY_ORDERBY_KEY = "bigpara:analiz.islemhacmi.orderby-{0}";
    public const string YATIRIMARACLARININGETIRILERI = "bigpara:borsa.yatirimaraclariningetirileri";
    public const string BORSA_HISSE_YUZEYSEL_ALL_KEY = "bigpara:borsa.hisse.yuzeysel.all"; // time out 60 sec 
    public const string BORSA_HISSE_YUZEYSEL_SEMBOL_KEY_BYID = "bigpara:borsa.hisse.yuzeysel.by.sembolId-{0}";
    public const string BORSA_HISSE_YUZEYSELONLINE_ALL_KEY = "bigpara:borsa.hisse.yuzeyselonline.all"; // time out 60 sec 
    public const string BORSA_ENDEKS_YUZYSEL_ALL_KEY = "bigpara:borsa.endeks.hisse.yuzeysel.{0}";
    public const string BORSA_HISSE_ENCOK_ARTAN_AZALANLAR_BY_ISLEMTIPI = "bigpara:borsa.hisse.encok.artan.azalan.by.islemtipi.top.{0}";
    public const string BORSA_HISSE_AGILRLIK_ORTALAMA_BY_ORDERBY = "bigpara:borsa.hisse.agirlik.ortalama.by.orderby.{0}";
    public const string BORSA_HISSE_ENCOK_ISLEMLER_ARTAN_AZALANLAR_BY_ISLEMTIPI_SEANS = "bigpara:borsa.hisse.encok.islemler.artan.azalan.by.islemtipi.seans.{0}-{1}";
    public const string BORSA_HISSE_AGILRLIK_ORTALAMA_BY_DIRECTION_PAGE_PAGESIZE_TOP = "bigpara:borsa.hisse.agirlik.ortalama.by.direction.page.pagesize.{0}-{1}-{2}";
    public const string BORSA_HISSE_INDIKATOR_BY_SEMBOL = "bigpara:borsa.hisse.indikator.by.sembol.{0}";
    public const string BORSA_HISSE_ENDKES_GECMIS_KAPANISLAR_BY_DATA_MARKETTYPE = "bigpara:borsa.hisse.endeks.gecmis.kapanislar.by.date.market.{0}-{1}-{2}";
    public const string BORSA_HISSE_ENDKES_GECMIS_KAPANISLAR_BY_MAX_DATE = "bigpara:borsa.hisse.endeks.gecmis.kapanislar.by.max_date";
    public const string BORSA_HISSE_BILANCO_BY_SEMBOL_DONEM_CINS_YIL = "bigpara:borsa.hisse.bilanco.by.sembol.donem.cins.yil.{0}.{1}.{2}.{3}";
    public const string BORSA_HISSE_BILANCO_BY_SEMBOL = "bigpara:borsa.hisse.bilanco.by.semboll.{0}";
    public const string BORSA_ENDEKS_BY_ALL_KEY = "bigpara:endeks.yuzeysel.hepsi.list";
    public const string BORSA_ENDEKS_YUZEYSEL_ARTAN_AZALAN_BY_ALL_KEY = "bigpara:endeks.yuzeysel.artan.azalan.list";
    public const string BORSA_USER_HISSE_YUZEYSEL__BY_USERID = "bigpara:user.hisse.yuzeysel.list.by.userid.{0}";
    public const string BORSA_USER_HISSE_YUZEYSEL_ONLINE_BY_USERID = "bigpara:user.hisse.yuzeysel.list.online.by.userid.{0}";
    public const string BORSA_USER_HISSE_HABERLER__BY_USERID = "bigpara:user.hisse.haberler.list.by.userid.{0}";
    public const string BORSA_USER_SEMBOL_HISSE_HABERLER_BY_USERID = "bigpara:user.sembol.hisse.haberler.list.by.userid.{0}";
    public const string BORSA_HISSE_YUZEYSEL_ISLEM_HACMI_FIYAT_ARTAN_AZALANLAR_BY_TIP = "bigpara:borsa.hisse.yuzeysel.islem.hacmi.fiyat.artan.azalan.list.by.tip.{0}";
    public const string BORSA_ANALIZ_PERFORMANS_BY_ENDEKS_SEMBOL_DATE_CURRENCY_ORDER_TIP = "bigpara:borsa.analiz.performanse.list.by.endeks.sembol.datestart.dateend.currency.order.{0}.{1}.{2}.{3}.{4}.{5}";
    public const string BORSA_ANALIZ_PERFORMANS_BY_ENDEKS_SEMBOL_LASTDATE_CURRENCY_ORDER_TIP = "bigpara:borsa.analiz.performanse.list.by.endeks.sembol.lastdatestart.lastdateend.currency.order.{0}.{1}.{2}.{3}.{4}.{5}";
    public const string BIGPARA_BORSA_PIYASA_BANDI_DEFAULT = @"bigpara:borsa.piyasa.bandi.default";
    public const string BIGPARA_BORSA_PIYASA_BANDI_DEFAULT_SHARED_LIST = @"bigpara:borsa.piyasa.bandi.varsayilan.hisse.list";
    public const string BORSA_HISSE_BILANCO_BY_SEMBOL_DONEM_CINS = "bigpara:borsa.hisse.bilanco.by.sembol.donem.cins.{0}.{1}.{2}";
    public const string BORSA_DUNYA_BORSALARI = "bigpara:borsa.dunya";
    #endregion

    #region Constants bist endeks
    public const string BIGPARA_BISTENDKES_TOP = "bigpara:bistendeks.top.bist.endeks";
    public const string BIGPARA_BISTENDKES_ARTAN_AZALANLAR_BY_TOP = "bigpara:bistendeks.artan.azalan.endeks.by.top.{0}";
    #endregion

    #region Constants Oran sektorel
    public const string BIGPARA_BORSA_ORAN_SEKTOREL_ALL_KEY = "bigpara:borsa.oran.sektorel";
    public const string BIGPARA_BORSA_ORAN_SEKTOREL_ALL_SEMBOL = "bigpara:borsa.oran.sektorel.by.sembol.{0}";
    public const string BIGPARA_BORSA_ORAN_SEKTOREL_ALL_SEKTORELID = "bigpara:borsa.oran.sektorel.by.sektorelid.{0}";
    #endregion

    #region Constants Otomatkik Teknik Yorum
    public const string BIGPARA_BORSA_OTOMATIK_TEKNIK_YORUM_BY_SEMBOL_TARIH = "bigpara:borsa.otomatik.teknik.yorum.by.sembol.tarih.{0}-{1}";
    public const string BIGPARA_BORSA_OTOMATIK_TEKNIK_YORUM_BY_SEMBOL = "bigpara:borsa.otomatik.teknik.yorum.by.sembol.{0}";
    #endregion

    #region Constants Doviz Service
    public const string DOVIZ_CEVIRICI_BY_WIDGETID = "bigpara:doviz.cevirici.widgetId.{0}";
    public const string DOVIZ_SERBEST_PIYASA_BY_WIDGETID = "bigpara:doviz.servestpiyasa.widgetId.{0}";
    public const string DOVIZ_MERKEZ_BANKASI_DOVIZ_BY_WIDGETID = "bigpara:doviz.merkezbankasi.widgetId.{0}";
    public const string DOVIZ_MERKEZ_BANKASI_DOVIZ_BY_DATETIME = "bigpara:doviz.merkezbankasi.tarih.{0}";
    public const string DOVIZ_MERKEZ_BANKASI_EKRANLAR = "bigpara:doviz.merkezbankasi.ekranlar";
    public const string DOVIZ_MERKEZ_BANKASI_SAYFALAR_BY_KOD = "bigpara:doviz.merkezbankasi.sayfalar.{0}";
    public const string DOVIZ_BANKALAR_ARASI_KUR_BY_PIYASAID_DOVIZTIPI = "bigpara:doviz.bankalar.arasi_kur.piyasaid.doviztipi.{0}.{1}";
    public const string DOVIZ_PARITELER_BY_WIDGETID = "bigpara:doviz.pariteler.widgetId.{0}";
    #endregion

    #region Constants Emtia Borsa Service
    public const string BIGPARA_BORSA_EMTIA_BORSALAR_ALL_KEY = "bigpara:borsa.emtia.dunya.borsalari.hepsi";
    public const string BIGPARA_BORSA_EMTIA_BORSALAR_SEMBOLLER_BY_WIDGETID = "bigpara:borsa.emtia.dunya.borsalari.emboller.by.widgetid.{0}";
    #endregion

    #region Constants Faiz Service
    public const string BIGPARA_BORSA_TAHVIL_BONO_OZET_BY_PAGECODE = "bigpara:borsa.faiz.bono.tahvil.by.code.{0}";
    public const string BIGPARA_KREDI_FAIZ_ORANI = "bigpara:kredi.faiz.orani";
    public const string BIGPARA_KREDI_FAIZ_ORANI_BY_CREDITTYPE = "bigpara:kredi.faiz.orani.by.credittype.{0}";
    public const string BIGPARA_KREDI_ADVISORY_BY_ID = "bigpara:kredi.advisory.by.id.{0}";
    public const string BIGPARA_KREDI_QUESTION_BY_ID = "bigpara:kredi.question.by.id.{0}";
    public const string BIGPARA_KREDI_ADVISORY_BY_CATEGORYID = "bigpara:kredi.advisory.by.categoryid.{0}";
    public const string BIGPARA_KREDI_QUESTIONS_BY_CATEGORYID = "bigpara:kredi.questions.by.categoryid.{0}";
    public const string BIGPARA_KREDI_INSTALLMENTS_BY_CREDITTYPE = "bigpara:kredi.installments.by.credittype.{0}";
    #endregion

    #region Constants matriks Haber service
    public const string BIGPARA_BORSA_MATRIK_HABERLER_BY_TOP = "bigpara:borsa.matriks.haberler.by.top.{0}";
    public const string BIGPARA_BORSA_MATRIK_HISSE_KAP_HABERLER_BY_SEMBOLID = "bigpara:borsa.matriks.hisse.kap.haberler.by.sembolid.{0}";
    public const string BIGPARA_BORSA_MATRIK_HISSE_KAP_HABERLER__DETAY_BY_ID = "bigpara:borsa.matriks.hisse.kap.haberler.detay.by.sembol.{0}";
    #endregion

    #region Constants Merkez Bankasi Service
    public const string BIGPARA_MERKEZBANKASI_BY_TARIH = "bigpara:borsa.merkezbankasi.tarih-{0}";
    #endregion

    #region Constants Parite Service
    public const string BIGPARA_BORSA_PARITIES_BY_WIDGETID = "bigpara:borsa.matriks.parities.list.by.widgetid.{0}";
    #endregion

    #region Constants Piyasa Takvimi
    public const string BIGPARA_BORSA_PIYASA_TAKVIMI_TARIH_ARALIGI_BY_TARIH_GUN_TARIHSAYISI = "bigpara:borsa.matriks.piyasa.takvimi.list.by.tarih.gun.tarihsayisi.{0}-{1}-{2}";
    public const string BIGPARA_BORSA_PIYASA_TAKVIMI_SAYFALI_BY_START_END = "bigpara:borsa.matriks.piyasa.takvimi.list.by.bas.bitis.{0}-{1}";
    public const string BIGPARA_BORSA_PIYASA_TAKVIMI_DETAY_BY_ID = "bigpara:borsa.matriks.piyasa.takvimi.detay.by.id.{0}";
    public const string BIGPARA_BORSA_PIYASA_TAKVIMI_TOP_COUNT = "bigpara:borsa.matriks.piyasa.takvimi.top.count";
    #endregion

    #region Constants Viopvarant service
    public const string BIGPARA_BORSA_ISVARANT_BY_WIDGETID = "bigpara:borsa.matriks.is.varant.list.by.widgetid.{0}";
    public const string BIGPARA_BORSA_VIOP_VARANT_ALL_KEY = "bigpara:borsa.matriks.voip.varant.list";
    public const string BIGPARA_BORSA_VARANT_DATAT_ALL_KEY = "bigpara:borsa.matriks.varant.data.list";
    #endregion

    #region Constants User
    public const string BIGPARA_REDIS_USER_GET_BY_HURRPASS_ID = "bigpara:user.redis.get.by.hurrpassid.{0}";
    public const string BIGPARA_REDIS_LICENCED_USER_GET_BY_HURRPASS_ID = "bigpara:licenced.user.redis.get.by.hurrpassid.{0}";
    #endregion

    public const string BIGPARA_TRADEMASTER_GETREPORT_KEY = "bigpara:trademaster.getreport.service.key";
    public const string KAPHABERLERI = "kaphaberleri";
    public const int CACHE_DURATION = 60; // In Seconds
    public const int CACHE_LONG_OUTPUT_DURATION = 3600; // In Seconds
    public const int CACHE_DATA_1DAY_DURATION = 86400;
    public const int CACHE_DATA_15SEC_DURATION = 15; // In Seconds
    public const int CACHE_DATA_30SEC_DURATION = 30; // In Seconds
    public const int CACHE_DATA_DURATION = 60; // In Seconds
    public const int CACHE_DATA_3DK_DURATION = 180; // In Seconds
    public const int CACHE_DATA_4DK_DURATION = 240; // In Seconds
    public const int CACHE_DATA_5DK_DURATION = 300; // In Seconds
    public const int CACHE_DATA_10DK_DURATION = 600; // In Seconds
    public const int CACHE_DATA_30DK_DURATION = 1800; // In Seconds
    public const int CACHE_LONG_DATA_DURATION = 3600;// In Seconds
    public const int CACHE_DATA_2HOUR_DURATION = 7200;// In Seconds
    public const int CACHE_6SAAT_DATA_DURATION = 6 * 3600;// In Seconds
    public const int CACHE_OUTPUT_DURATION = 60; // In Seconds
    public const int CACHE_TIMELESS = -1; // In Seconds
    public static string GetAllNewsKey()
    {
        return _redisallnewsList;
    }
    public static string GetNewsDetailKey(string newsType, object newsId)
    {
        return string.Format(_redisNewsDetailKey, newsType, newsId);
    }
    public static string GetMostClickedStockKey
    {
        get { return _redisMostClickedStock; }
    }
    public static string GetMostReadNewsKey()
    {
        return _redisMostReadNewsKey;
    }
}
