using System.Threading;
using System.Threading.Tasks;
using Bigpara.Core.Application.Features.Tools.Queries;
using Bigpara.Core.Application.Features.Tools.ViewModels;
using Bigpara.Service.Api.Controllers;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace Bigpara.Service.Api.Tests.Controllers
{
    public class ToolsControllerTests
    {
        private readonly Mock<IMediator> _mediatorMock;
        private readonly ToolsController _controller;

        public ToolsControllerTests()
        {
            _mediatorMock = new Mock<IMediator>();
            _controller = new ToolsController(_mediatorMock.Object);
        }

        [Fact]
        public async Task Exchange_ReturnsOkResult_WithExpectedData()
        {
            // Arrange
            var query = new GetExchangeRatesQuery
            {
                Source = "USD",
                Amount = 100,
                TargetSymbols = "EUR,TRY"
            };

            var response = new GetExchangeRatesQueryResponse
            {
                Data = new List<GetExchangeRatesQueryItemModel>()
            };

            _mediatorMock
                .Setup(m => m.Send(query, It.IsAny<CancellationToken>()))
                .ReturnsAsync(response);

            // Act
            var result = await _controller.Exchange(query);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            Assert.Equal(response, okResult.Value);
        }
    }
}
