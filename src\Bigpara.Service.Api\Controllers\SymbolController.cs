﻿using Bigpara.Core.Application.Features.Symbols.Queries;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace Bigpara.Service.Api.Controllers
{
    [Route("api/symbol")]
    [ApiController]
    public class SymbolController : ControllerBase
    {
        private readonly ILogger<NewsController> _logger;
        private readonly IMediator _mediator;
        public SymbolController(ILogger<NewsController> logger, IMediator mediator)
        {
            _logger = logger;
            _mediator = mediator;
        }
        [HttpGet("search")]
        public async Task<IActionResult> Search(GetSymbolSearchQuery query)
        {

            var result = await _mediator.Send(query);

            return Ok(result);

        }


        [HttpGet("trends")]
        public async Task<IActionResult> Trends(GetTrendsSymbolQuery query)
        {
            var result = await _mediator.Send(query);

            return Ok(result);

        }
    }
}
