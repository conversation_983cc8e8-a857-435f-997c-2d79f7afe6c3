﻿using Bigpara.Core.Application.Contracts.Matriks.Piyasalar;
using Bigpara.Core.Application.Contracts.Matriks.Sembol;
using Bigpara.Core.Application.Features.Symbols.Queries.ViewModels;
using MediatR;

namespace Bigpara.Core.Application.Features.Symbols.Queries;


#region Query
public class GetTrendsSymbolQuery : IRequest<SymbolSearchQueryViewModel>
{
    public string Type { get; set; }
    public int  Size { get; set; }

}

#endregion

#region Handler
public class GetTrendsSymbolQueryHandler : IRequestHandler<GetTrendsSymbolQuery, SymbolSearchQueryViewModel>
{
    private readonly ISembolRepository _sembollerService;
    private readonly IPiyasaRepository _piyasaRepository;
    public GetTrendsSymbolQueryHandler(ISembolRepository sembollerService, IPiyasaRepository piyasaRepository)
    {
        _sembollerService = sembollerService;
        _piyasaRepository = piyasaRepository;
    }

    public async Task<SymbolSearchQueryViewModel> Handle(GetTrendsSymbolQuery request, CancellationToken cancellationToken)
    {
        var cachedIndexSymbols = await _sembollerService.GetSembollerList();
        var quearyableSymbols = cachedIndexSymbols.AsQueryable().Where(p=> string.IsNullOrEmpty(request.Type) ||  p.SEMBOLTYPE==request.Type);

        if (request.Size > 0)
            quearyableSymbols = quearyableSymbols.Take(request.Size);

        // Paging
        var pagedSymbols = quearyableSymbols
            .OrderByDescending(p => p.SEMBOLID) //TODO : Click Or Search Count Or Percent Desc
            .ToList();

        var sembols = new List<SembolItemModel>();
        foreach (var x in pagedSymbols)
        {
            var piyasa = await _piyasaRepository.GetById(x.PIYASAID);
            sembols.Add(new SembolItemModel
            {
                Symbol = x.SEMBOL,
                Description = x.ACIKLAMA,
                Icon = $"{x.SEMBOLID}.png",
                Id = x.SEKTORID,
                Bazaar = piyasa?.PIYASAKODU,
                Exchange = x.PIYASAID != 3 ? "BIST" : "SPOT",
                Type = x.SEMBOLTYPE,
            });
        }

        var response = new SymbolSearchQueryViewModel
        {
            Data = sembols
        };

        return response;
    }
}

#endregion


