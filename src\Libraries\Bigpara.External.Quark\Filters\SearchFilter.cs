﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.External.Quark.Filters
{
    public class SearchFilter
    {
        public string Keyword { get; set; }
        public string Fields { get; set; }
        public string Operators { get; set; }
        public string Types { get; set; }
        public string SortField { get; set; }
        public int SortOrder { get; set; }
        public int PageIndex { get; set; }
        public int PageSize { get; set; }
        public string WriterId { get; set; }
        public string Year { get; set; }
        public string Category { get; set; }
        public string ReturnFields { get; set; }
        public bool WithAggrigation { get; set; }
        public bool WithDefaultData { get; set; }
        public bool ExactResult { get; set; }
        public string ContentTag { get; set; }
        public string NotInTags { get; set; }
        public bool PrefixSearch { get; set; }
        public string Links { get; set; }

    }
}
