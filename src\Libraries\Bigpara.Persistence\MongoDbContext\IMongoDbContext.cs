﻿using MongoDB.Bson;
using MongoDB.Driver;

namespace Bigpara.Persistence.MongoDbContext;

public interface IMongoDbContext
{
    IMongoCollection<T> GetCollection<T>(string collectionName) where T : class;
    Task<T> GetByIdAsync<T>(string collectionName, ObjectId id) where T : class;
    Task<IEnumerable<T>> GetAllAsync<T>(string collectionName, string application = "com.bigpara") where T : class;
    Task<IEnumerable<T>> FindAsync<T>(string collectionName, FilterDefinition<T> filter) where T : class;
    Task<IEnumerable<T>> FindAsync<T>(string collectionName) where T : class;
    Task<T> FindOneAsync<T>(string collectionName, FilterDefinition<T> filter) where T : class;
    Task InsertAsync<T>(string collectionName, T entity) where T : class;
    Task InsertManyAsync<T>(string collectionName, IEnumerable<T> entities) where T : class;
    Task<bool> UpdateAsync<T>(string collectionName, ObjectId id, T entity) where T : class;
    Task<bool> DeleteAsync<T>(string collectionName, ObjectId id) where T : class;
    Task<long> DeleteManyAsync<T>(string collectionName, FilterDefinition<T> filter) where T : class;
    Task<long> GetNextIncrementIdAsync(string collectionName, string application);
}