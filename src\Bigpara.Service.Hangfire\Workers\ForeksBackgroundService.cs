﻿using ForeksPubsubSdk;
using System.Text;
using System.Text.Json;
using Bigpara.Jobs.Hangfire.Models;
using Bigpara.Service.Hangfire.Services.Interfaces;
using Bigpara.Service.Hangfire.Services;
using Bigpara.Notifications;
using Bigpara.Service.Hangfire.Infrastructure.Dtos.Foreks;

namespace Bigpara.Jobs.Hangfire.Workers
{
    public class ForeksBackgroundService : BackgroundService
    {
        private static PubsubClient foreksPubsubClient;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ForeksBackgroundService> _logger;
        private HttpClient _client;
        private List<string> fields;
        private static readonly TimeZoneInfo TurkeyTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");
        private readonly INotificationService _notificationService;


        public ForeksBackgroundService(
            IHttpClientFactory httpClientFactory,
            IServiceProvider serviceProvider,
            ILogger<ForeksBackgroundService> logger,
            INotificationService notificationService)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _client = httpClientFactory.CreateClient("BigparaSignalrService");
            fields = new List<string>() {
                 Fields.Ask,
                 Fields.Bid,
                 Fields.Direction,
                 Fields.Code,
                 Fields.Low,
                 Fields.High,
                 Fields.DailyChangePercent,
                 Fields.DateTime,
                 Fields.SecurityType,
                 Fields.MarketSector,
                 Fields.ShortMarketCode,
                 Fields.Type,
                 Fields.SecurityGroup,
                 Fields.Group,
                 Fields.GrossSettlement,
                 Fields.VWAP,
                 Fields.LowerLimit,
                 Fields.UpperLimit,
                 Fields.MTDHigh,
                 Fields.MTDLow,
                 Fields.PreviousClose,
                 Fields.PreviousMonthClose,
                 Fields.PreviousWeekClose,
                 Fields.PreviousYearClose,
                 Fields.WTDHigh,
                 Fields.WTDLow,
                 Fields.YTDHigh,
                 Fields.YTDLow,
                 Fields.Close,
                 Fields.TotalTurnover,
                 Fields.TotalVolume
            };
            _notificationService = notificationService;
        }

        void ForeksPubsubClient_OnNotification(object sender, ResponseArgs
e)
        {
            _logger.LogError($"ForeksPubsubClient_OnNotification : Code {e.Code}, RawString {e.RawString}");
            _notificationService.NotifyErrorAsync("ForeksPubsubClient_OnNotification", $"ForeksPubsubClient_OnNotification : Code {e.Code}, RawString {e.RawString}").ConfigureAwait(false).GetAwaiter().GetResult();
            Console.WriteLine("OnNotification: " + e.ToString());
        }
        void ForeksPubsubClient_OnSymbolRemove(object sender, ResponseArgs
      e)
        {
            _logger.LogError($"ForeksPubsubClient_OnSymbolRemove : Code {e.Code}, RawString {e.RawString}");
            _notificationService.NotifyErrorAsync("ForeksPubsubClient_OnSymbolRemove", $"ForeksPubsubClient_OnSymbolRemove : Code {e.Code}, RawString {e.RawString}").ConfigureAwait(false).GetAwaiter().GetResult();
            Console.WriteLine("OnSymbolAdd: " + e.ToString());
        }
        void ForeksPubsubClient_OnSymbolAdd(object sender, ResponseArgs e)
        {
            _logger.LogError($"ForeksPubsubClient_OnSymbolAdd : Code {e.Code}, RawString {e.RawString}");
            _notificationService.NotifyErrorAsync("ForeksPubsubClient_OnSymbolAdd", $"ForeksPubsubClient_OnSymbolAdd : Code {e.Code}, RawString {e.RawString}").ConfigureAwait(false).GetAwaiter().GetResult();
            Console.WriteLine("OnSymbolAdd: " + e.ToString());
        }
        void ForeksPubsubClient_OnError(object sender, ResponseArgs e)
        {
            _logger.LogError($"ForeksPubsubClient_OnError : Code {e.Code}, RawString {e.RawString}");
            _notificationService.NotifyErrorAsync("ForeksPubsubClient_OnError", $"ForeksPubsubClient_OnError : Code {e.Code}, RawString {e.RawString}").ConfigureAwait(false).GetAwaiter().GetResult();
            Console.WriteLine("OnError: " + e.ToString());
        }
        void ForeksPubsubClient_OnDisconnect(object sender, ResponseArgs
      e)
        {
            Console.WriteLine("OnDisconnect: " + e.ToString());
        }
        void ForeksPubsubClient_OnHeartbeat(object sender, ResponseArgs e)
        {
            Console.WriteLine("OnHeartbeat: " + e.ToString());
        }
        void ForeksPubsubClient_OnPong(object sender, ResponseArgs e)
        {
            Console.WriteLine("OnPong: " + e.ToString());
            long now = (long)(DateTime.UtcNow - new DateTime(1970, 1,
           1)).TotalMilliseconds;
            long sent = long.Parse(e.Result.ToString());
            long cycle = now - sent;
            Console.WriteLine("OnPong: a cycle takes " + cycle + "miliseconds");
        }
        void ForeksPubsubClient_OnLogin(object sender, ResponseArgs e)
        {
            Console.WriteLine("OnLogin: " + e.ToString());
            if (e.Success)
            {
                //Parite 
                var hisseler = GetCodes("https://feed-definition.foreks.com/symbol/search?domain=BIST&exchange=BIST&marketSector=Equity&status=ACTIVE&fields=code,security,securityDesc,securityType,tag");
                var endeksler = GetCodes("https://feed-definition.foreks.com/symbol/search?domain=BIST&exchange=BIST&marketSector=Index&fields=code,security,securityDesc,securityType,tag&status=ACTIVE");

                var stocks = new List<string>();

                stocks.AddRange(hisseler);
                stocks.AddRange(endeksler);


                var pageSize = 1000;
                var pageCount = stocks.Count / pageSize;
                if (stocks.Count % pageSize > 0)
                {
                    pageCount++;
                }
                for (int i = 0; i < pageCount; i++)
                {
                    foreksPubsubClient.Subscribe(stocks.Skip(i * pageSize).Take(pageSize).ToList(), fields);
                }


                foreksPubsubClient.Ping(((long)(DateTime.UtcNow - new DateTime(1970, 1,
    1)).TotalMilliseconds).ToString());
            }
        }
        void ForeksPubsubClient_OnMessage(object sender, ResponseArgs e)
        {
            Console.WriteLine("OnMessage: " + e.ToString());
        }
        void ForeksPubsubClient_OnData(object sender, ResponseArgs e)
        {
            try
            {
                var results = (Dictionary<string, string>)e.Result;
                if (results.ContainsKey(Fields.DateTime))
                {
                    SendRealTimeServie(results).ConfigureAwait(false).GetAwaiter().GetResult();
                    SaveStorage(results).ConfigureAwait(false).GetAwaiter().GetResult();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex.Message);
                _notificationService.NotifyErrorAsync("ForeksPubsubClient_OnData", $"ForeksPubsubClient_OnData : {ex.Message}").ConfigureAwait(false).GetAwaiter().GetResult();
            }
        }

        private async Task SaveStorage(Dictionary<string, string> results)
        {


            using (var scope = _serviceProvider.CreateScope())
            {
                var hisseService = scope.ServiceProvider.GetRequiredService<IHisseService>();
                var pariteService = scope.ServiceProvider.GetRequiredService<IPariteService>();
                var endeksService = scope.ServiceProvider.GetRequiredService<IEndeksService>();
                var serbestPiyasaService = scope.ServiceProvider.GetRequiredService<ISerbestPiyasaService>();

                var _symbolService = scope.ServiceProvider.GetRequiredService<ISymbolService>();
                var _configuration = scope.ServiceProvider.GetRequiredService<IConfiguration>();
                var symbol = await _symbolService.GetSymbolByIdAsync(results["_i"]);

                if (symbol == null)
                    return;

                if (!results.ContainsKey("MS"))
                {
                    results.Add("MS", symbol.Sembol.MarketSector);

                }

                if (!results.ContainsKey("SY"))
                {
                    results.Add("SY", symbol.Sembol.SecurityType);
                }

                if (!results.ContainsKey("E"))
                {
                    results.Add("E", symbol.Sembol.Sembol);
                }

                results.Add("subMarket", symbol.Sembol.ImkbPazarKodString);


                switch (symbol.Sembol.MarketSector)
                {
                    case "Equity":

                        if (symbol.Sembol.SecurityType == "Warrant")
                        {
                            await SaveStorageWarrant(results, hisseService);
                            break;
                        }
                        else if (symbol.Sembol.SecurityType == "Fund")
                        {
                            await SaveStorageFund(results, hisseService);
                            break;
                        }
                        else
                        {
                            await SaveStorageImkb(results, hisseService);
                        }
                        break;
                    case "Index":
                        await SaveStorageIndex(results, endeksService);
                        break;
                    case "Currency":
                        results["legacyCode"] = symbol.Sembol.ForeksKodYeni;

                        if (symbol.Sembol.Exchange.Equals("GrandBazaar"))
                            await SaveStorageSerbest(results, serbestPiyasaService);
                        else
                            await SaveStorageCurrency(results, pariteService);

                        break;
                    default:
                        break;
                }

                //Tanım önce veya sonra olabilir.
                await Task.CompletedTask;
            }
        }

        private async Task SaveStorageCurrency(Dictionary<string, string> results, IPariteService pariteService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await pariteService.Change(yuzeyselDto);
            await Task.CompletedTask;

        }

        private async Task SaveStorageSerbest(Dictionary<string, string> results, ISerbestPiyasaService serbestPiyasaService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await serbestPiyasaService.Change(yuzeyselDto);

            await Task.CompletedTask;
        }

        private async Task SaveStorageIndex(Dictionary<string, string> results, IEndeksService endeksService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await endeksService.Change(yuzeyselDto);
            await Task.CompletedTask;

        }

        private async Task SaveStorageImkb(Dictionary<string, string> results, IHisseService hisseService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await hisseService.Change(yuzeyselDto);
            await Task.CompletedTask;
        }

        private static YuzeyselDto BuildYuzeyselDto(Dictionary<string, string> results)
        {
            var yuzeyselDto = new YuzeyselDto();
            yuzeyselDto.SEMBOL = results[Fields.Code];
            yuzeyselDto.UnixTime = (long)Convert.ToDouble(results[Fields.DateTime]);
            yuzeyselDto.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
                                DateTimeOffset.FromUnixTimeMilliseconds((long)Convert.ToDouble(results[Fields.DateTime])).UtcDateTime,
                                TurkeyTimeZone);

            if (results.ContainsKey("subMarket"))
                yuzeyselDto.HisseGrubu = !string.IsNullOrEmpty(results["subMarket"]) ? results["subMarket"][0] : default(char);

            if (results.ContainsKey("legacyCode"))
            {
                yuzeyselDto.LegacyCode = results["legacyCode"];
            }

            if (results.ContainsKey(Fields.Ask) && decimal.TryParse(results[Fields.Ask], out decimal a))
            {
                yuzeyselDto.SATIS = a;
            }

            if (results.ContainsKey(Fields.TotalTurnover) && decimal.TryParse(results[Fields.TotalTurnover], out decimal tT))
            {
                yuzeyselDto.HACIMTL = tT;
            }

            if (results.ContainsKey(Fields.TotalVolume) && decimal.TryParse(results[Fields.TotalVolume], out decimal tV))
            {
                yuzeyselDto.HACIMLOT = tV;
            }

            if (results.ContainsKey(Fields.Bid) && decimal.TryParse(results[Fields.Bid], out decimal b))
            {
                yuzeyselDto.ALIS = b;
            }

            if (results.ContainsKey(Fields.Low) && decimal.TryParse(results[Fields.Low], out decimal l))
            {
                yuzeyselDto.DUSUK = l;
            }

            if (results.ContainsKey(Fields.High) && decimal.TryParse(results[Fields.High], out decimal h))
            {
                yuzeyselDto.YUKSEK = h;
            }

            if (results.ContainsKey(Fields.VWAP) && decimal.TryParse(results[Fields.VWAP], out decimal d))
            {
                yuzeyselDto.AORT = d;
            }

            if (results.ContainsKey(Fields.Open) && decimal.TryParse(results[Fields.Open], out decimal pa))
            {
                yuzeyselDto.ACILIS = pa;
            }

            if (results.ContainsKey(Fields.Close) && decimal.TryParse(results[Fields.Close], out decimal p))
            {
                yuzeyselDto.KAPANIS = p;
            }

            if (results.ContainsKey(Fields.LowerLimit) && decimal.TryParse(results[Fields.LowerLimit], out decimal c))
            {
                yuzeyselDto.TABAN = c;
            }
            if (results.ContainsKey(Fields.UpperLimit) && decimal.TryParse(results[Fields.UpperLimit], out decimal u))
            {
                yuzeyselDto.TAVAN = u;
            }

            if (results.ContainsKey(Fields.WTDLow) && decimal.TryParse(results[Fields.WTDLow], out decimal dc))
            {
                yuzeyselDto.HAFTADUSUK = dc;
            }

            if (results.ContainsKey(Fields.WTDHigh) && decimal.TryParse(results[Fields.WTDHigh], out decimal dh))
            {
                yuzeyselDto.HAFTAYUKSEK = dh;
            }

            if (results.ContainsKey(Fields.MTDLow) && decimal.TryParse(results[Fields.MTDLow], out decimal dcp))
            {
                yuzeyselDto.AYDUSUK = dcp;
            }

            if (results.ContainsKey(Fields.MTDHigh) && decimal.TryParse(results[Fields.MTDHigh], out decimal dhp))
            {
                yuzeyselDto.AYYUKSEK = dhp;
            }

            if (results.ContainsKey(Fields.YTDLow) && decimal.TryParse(results[Fields.YTDLow], out decimal dcy))
            {
                yuzeyselDto.YILDUSUK = dcy;
            }

            if (results.ContainsKey(Fields.YTDHigh) && decimal.TryParse(results[Fields.YTDHigh], out decimal dhy))
            {
                yuzeyselDto.YILYUKSEK = dhy;
            }

            if (results.ContainsKey(Fields.PreviousMonthClose) && decimal.TryParse(results[Fields.PreviousMonthClose], out decimal dmcp))
            {
                yuzeyselDto.ONCEKIAYKAPANIS = dmcp;
            }
            if (results.ContainsKey(Fields.PreviousYearClose) && decimal.TryParse(results[Fields.PreviousYearClose], out decimal dycp))
            {
                yuzeyselDto.ONCEKIYILKAPANIS = dycp;
            }

            if (results.ContainsKey(Fields.PreviousWeekClose) && decimal.TryParse(results[Fields.PreviousWeekClose], out decimal dwcp))
            {
                yuzeyselDto.ONCEKIHAFTAKAPANIS = dwcp;
            }

            return yuzeyselDto;
        }

        private async Task SaveStorageFund(Dictionary<string, string> results, IHisseService hisseService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await hisseService.Change(yuzeyselDto);
            await Task.CompletedTask;
        }

        private async Task SaveStorageWarrant(Dictionary<string, string> results, IHisseService hisseService)
        {
            var yuzeyselDto = BuildYuzeyselDto(results);
            await hisseService.Change(yuzeyselDto);
            await Task.CompletedTask;
        }

        private async Task SendRealTimeServie(Dictionary<string, string> results)
        {
            try
            {
                var apiUrl = "api/trade";
                var json = JsonSerializer.Serialize(results);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                Console.WriteLine($"Gönderilen veri: {json}");

                var response = await _client.PostAsync(apiUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Başarılı! Yanıt: {responseContent}");
                }
                else
                {
                    Console.WriteLine($"Hata: {response.StatusCode}, {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"İstek gönderilirken hata oluştu: {ex.Message}");
            }
        }

        public List<string> GetCodes(string filterUrl)
        {
            var exludeSecurtySembols = new List<string>() { "TE", "F1", "F2", "TF", "TR", "S1", "O", "V" };

            using (HttpClient client = new HttpClient())
            {
                var response = client.GetAsync(filterUrl).Result;
                if (response.IsSuccessStatusCode)
                {
                    var content = response.Content.ReadAsStringAsync().Result;
                    var stocks = JsonSerializer.Deserialize<List<ForeksSymbol>>(content);
                    if (stocks == null)
                    {
                        return new List<string>();
                    }
                    return stocks.Where(s => !exludeSecurtySembols.Contains(s.Security))
                                 .Where(p => !p.tag.Contains("ReturnIndex"))
                                 .Select(s => s._Id)
                                 .ToList();
                }
                else
                {
                    Console.WriteLine("Hata: " + response.StatusCode);
                    return new List<string>();
                }
            }
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {

            try
            {

                ConnectionOptions connectionOptions = new ConnectionOptions("enterprise.foreks.com", 9000, "hurriyetbigpara", "hrrt52ncx50", "hurriyetbigpara_webfeed", "1.0.1");

                foreksPubsubClient = new PubsubClient(connectionOptions);

                foreksPubsubClient.OnLogin += ForeksPubsubClient_OnLogin;
                foreksPubsubClient.OnHeartbeat += ForeksPubsubClient_OnHeartbeat;
                foreksPubsubClient.OnPong += ForeksPubsubClient_OnPong;
                foreksPubsubClient.OnData += ForeksPubsubClient_OnData;
                foreksPubsubClient.OnDisconnect += ForeksPubsubClient_OnDisconnect;
                foreksPubsubClient.OnError += ForeksPubsubClient_OnError;
                foreksPubsubClient.OnMessage += ForeksPubsubClient_OnMessage;
                foreksPubsubClient.OnNotification += ForeksPubsubClient_OnNotification;
                foreksPubsubClient.OnSymbolAdd += ForeksPubsubClient_OnSymbolAdd;
                foreksPubsubClient.OnSymbolRemove += ForeksPubsubClient_OnSymbolRemove;

                foreksPubsubClient.Start();
            }
            catch (Exception exception)
            {
                Console.WriteLine(exception.Message);
            }

            await Task.CompletedTask;
        }
    }
}


