﻿
namespace Bigpara.Cache.Interfaces;

public interface IRedisCacheService
{
    bool ContainsKey(string listKey);
    void Add<T>(string key, T value);
    void Add<T>(string key, T value, TimeSpan cacheTime);
    void Add<T>(string key, T value, int cacheTime);
    bool IsSet(string key);
    void Clear(string key);
    void Remove(string key);
    T Get<T>(string key);
    T Get<T>(string key, Func<T> acquire);
    T Get<T>(string key, TimeSpan cacheTime, Func<T> acquire);
    T Get<T>(string key, int cacheDuration, Func<T> acquire);
    void Update<T>(string listkey, T value, Func<T, bool> filterFunction);
    void EnqueueToTypedList<T>(string key, IEnumerable<T> value);
    void AddTypedList<T>(string listKey, IEnumerable<T> value);
    void AddTypedList<T>(string listKey, IEnumerable<T> value, int timeout);
    void AddTypedList<T>(string listKey, IEnumerable<T> value, int timeout, bool isNewList = false);
    IList<T> GetTypedList<T>(string listKey, short page, int pageSize, out int totalItemCount);
    IList<T> GetTypedList<T>(string listKey);
    IList<T> GetTypedList<T>(string listKey, Func<T, bool> filterFunction);
    IList<T> GetTypedList<T>(string listKey, Func<T, bool> filterFunction, short page, int pageSize, out int totalItemCount);
    void RemoveFromList<T>(string listKey, Func<T, bool> filterFunction);
    IList<T> GetTypedOrderList<T>(string listKey, Func<T, object> expression, OrderByDirections direction);
    IList<T> GetTypedOrderList<T>(string listKey, Func<T, object> expression, OrderByDirections direction, int topN);
    void Store<T>(string hashId, Dictionary<string, T> mapValues);
    T GetValueFromHash<T>(string hashId, string key);
}
