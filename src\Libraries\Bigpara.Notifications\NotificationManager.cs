﻿using Bigpara.Notifications.Email;
using Bigpara.Notifications.Teams;

namespace Bigpara.Notifications;

/// <summary>
/// Bildirimlerin e-posta ve Microsoft Teams üzerinden iletilmesini sağlayan yöneticidir.
/// Hata ve bilgilendirme mesajlarını belirli kanallar aracılığıyla iletmek için kullanılır.
/// </summary>
public class NotificationManager : INotificationService
{
    private readonly IEmailService _emailService;
    private readonly ITeamsNotificationService _teamsNotificationService;

    /// <summary>
    /// <see cref="NotificationManager"/> sınıfının yeni bir örneğini başlatır.
    /// </summary>
    /// <param name="emailService">E-posta gönderim servisi.</param>
    /// <param name="teamsNotificationService">Microsoft Teams bildirim servisi.</param>
    public NotificationManager(IEmailService emailService, ITeamsNotificationService teamsNotificationService)
    {
        _emailService = emailService;
        _teamsNotificationService = teamsNotificationService;
    }

    /// <summary>
    /// Belirtilen iş için hata mesajını seçilen kanallar üzerinden gönderir.
    /// </summary>
    /// <param name="jobName">İlgili işin adı.</param>
    /// <param name="errorMessage">Gönderilecek hata mesajı.</param>
    /// <param name="channel">Bildirim gönderilecek kanal (Email, Teams veya Both(Her ikisi)).</param>
    public async Task NotifyErrorAsync(string jobName, string errorMessage, NotificationChannel channel = NotificationChannel.Both)
    {
        await NotifyAsync(jobName, errorMessage, channel, isError: true);
    }

    /// <summary>
    /// Belirtilen iş için bilgi mesajını seçilen kanallar üzerinden gönderir.
    /// </summary>
    /// <param name="jobName">İlgili işin adı.</param>
    /// <param name="infoMessage">Gönderilecek bilgi mesajı.</param>
    /// <param name="channel">Bildirim gönderilecek kanal (Email, Teams veya Both(Her ikisi)).</param>
    public async Task NotifyInfoAsync(string jobName, string infoMessage, NotificationChannel channel = NotificationChannel.Both)
    {
        await NotifyAsync(jobName, infoMessage, channel, isError: false);
    }

    /// <summary>
    /// Ortak bildirim gönderim metodudur. Hata veya bilgi mesajlarını belirli kanallara gönderir.
    /// </summary>
    /// <param name="jobName">İlgili işin adı.</param>
    /// <param name="message">Gönderilecek mesaj.</param>
    /// <param name="channel">Bildirim gönderilecek kanal (Email, Teams veya Both(Her ikisi)).</param>
    /// <param name="isError">Mesajın hata mı yoksa bilgi mesajı mı olduğunu belirtir.</param>
    private async Task NotifyAsync(string jobName, string message, NotificationChannel channel, bool isError)
    {
        if (channel == NotificationChannel.Email || channel == NotificationChannel.Both)
        {
            if (isError)
                await _emailService.SendErrorEmailAsync(jobName, message);
            else
                await _emailService.SendInfoEmailAsync(jobName, message);
        }

        if (channel == NotificationChannel.Teams || channel == NotificationChannel.Both)
        {
            if (isError)
                await _teamsNotificationService.SendErrorNotificationAsync(jobName, message);
            else
                await _teamsNotificationService.SendInfoNotificationAsync(jobName, message);
        }
    }
}
