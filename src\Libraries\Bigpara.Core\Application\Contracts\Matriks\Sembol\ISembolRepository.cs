﻿using Bigpara.Core.Dtos.Matriks;
using Bigpara.Domain;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.Definitions;
using Bigpara.Domain.Redis;
using System.Linq.Expressions;

namespace Bigpara.Core.Application.Contracts.Matriks.Sembol;

public interface ISembolRepository
{
    IQueryable<Semboller> Query(Expression<Func<Semboller, bool>> filter = null, Func<IQueryable<Semboller>, IOrderedQueryable<Semboller>> orderBy = null);
    Task<List<Semboller>> GetSembolsGrafikGunluks(DateTime dateTime);
    Task<List<Endeks>> GetSembolByEndeks(string endeks);
    Task<Semboller> GetSembollerById(int sembolId);
    Task<Semboller> GetSembollerBySembolId(int sembolId);
    Task<Semboller> GetSembollerByCode(string code);
    Task<List<Semboller>> GetHisseSembollerByPiyasaIdList(int piyasaId);
    Task<List<Endeks>> GetSembolEndekslerListesi();
    Task<List<Endeks>> GetEndeksList();
    Task<List<SembolEndeks>> GetSembolEndekses(string sembol);
    Task<RedisReadCount> GetSymbolReadCountsAllPeriods(int symbolId);
    Task<int> InsertSymbolReadCount(SymbolReadCount symbolReadCount);
    Task<List<YatirimAraclari>> GetYatirimAraclariList();
    Task<SembollerEk> GetSembollerEkData(int sembolId);
    Task<int> UpdateSymbolReadCount(int sembolId, DateTime countDate);
    Task<List<SymbolCountSummary>> GetSembolReadCountSummaries();
    Task<KisaTanimSemboller> GetSymbolSummaryBySymbol(string sembol);

    Task<List<Semboller>> GetSembolHisseListesi();
    Task<List<SembolDto>> GetSembollerList();
}