﻿using StackExchange.Redis;
using Bigpara.Cache.Redis.Services;
using Bigpara.Cache.Interfaces;
using Microsoft.Extensions.Configuration;
using System.Text;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Bigpara.Cache.Redis.Services;

public class RedisCacheService : IRedisCacheService
{
    private readonly IDatabase _database;
    private readonly ILogger<RedisCacheService> _logger;
    private readonly TimeSpan _defaultCacheDuration = TimeSpan.FromSeconds(15);

    public RedisCacheService(IConnectionMultiplexer connectionMultiplexer, IConfiguration configuration, ILogger<RedisCacheService> logger)
    {
        _database = connectionMultiplexer.GetDatabase(int.Parse(configuration["RedisDefaultDbIndex"] ?? "0"));
        _logger = logger;
    }

    public bool ContainsKey(string listKey)
    {
        var keyIsLive = _database.KeyTimeToLive(listKey);
        if (keyIsLive == null)
        {
            Remove(listKey);
            return false;
        }
        else
        {
            return _database.KeyExists(listKey);

        }
    }

    public void Remove(string key)
    {
        Clear(key);
    }

    public void Clear(string key)
    {
        if (_database.KeyExists(key))
        {
            _database.KeyDelete(key);
        }
    }

    public T Get<T>(string key)
    {
        try
        {
            var redisValue = _database.StringGet(key);
            if (redisValue.HasValue)
            {
                return JsonConvert.DeserializeObject<T>(redisValue.ToString());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Redis key: {Key}", key);
        }

        return default;
    }

    public T Get<T>(string key, int retry = 2)
    {
        try
        {
            var redisValue = _database.StringGet(key);
            if (redisValue.HasValue)
            {
                return JsonConvert.DeserializeObject<T>(redisValue.ToString());
            }
        }
        catch (Exception)
        {
            return --retry == 0 ? default : Get<T>(key, retry);
        }
        return default;
    }

    public T Get<T>(string key, Func<T> acquire)
    {
        if (_database.KeyExists(key))
        {
            var redisValue = _database.StringGet(key);
            if (redisValue.HasValue)
            {
                return JsonConvert.DeserializeObject<T>(redisValue.ToString());
            }
        }

        var result = acquire();

        if (!Equals(result, null))
        {
            Add(key, result);
        }

        return result;
    }

    public T Get<T>(string key, TimeSpan cacheTime, Func<T> acquire)
    {
        if (_database.KeyExists(key))
        {
            var redisValue = _database.StringGet(key);
            if (redisValue.HasValue)
            {
                return redisValue.To<T>();
            }
        }

        var result = acquire();

        if (!Equals(result, null) && cacheTime.TotalSeconds > 0)
        {
            Add(key, result, cacheTime);
        }

        return result;
    }

    public T Get<T>(string key, int cacheDuration, Func<T> acquire)
    {
        if (_database.KeyExists(key))
        {
            var redisValue = _database.StringGet(key);
            if (redisValue.HasValue)
            {
                return redisValue.To<T>();
            }
        }

        var result = acquire();

        var cacheTime = new TimeSpan(0, 0, 0, cacheDuration);
        if (!Equals(result, null) && cacheTime.TotalSeconds > 0)
        {
            Add(key, result, cacheTime);
        }

        return result;
    }

    public void Add<T>(string key, T value)
    {
        Add(key, value, _defaultCacheDuration);
    }

    public void Add<T>(string key, T value, TimeSpan cacheTime)
    {
        if (EqualityComparer<T>.Default.Equals( value,default(T))) return;

        var jsonValue = JsonConvert.SerializeObject(value);
        _database.StringSet(key, jsonValue, cacheTime);
    }

    public void Add<T>(string key, T value, int cacheTime)
    {
        if (EqualityComparer<T>.Default.Equals(value, default(T))) return;
        
        var jsonValue = JsonConvert.SerializeObject(value);

        if (cacheTime > 0)
        {
            var expireDate = new TimeSpan(0, 0, 0, cacheTime);
            _database.StringSet(key, jsonValue, expireDate);
        }
        else
        {
            _database.StringSet(key, jsonValue);
        }
    }

    public void AddTypedList<T>(string listKey, IEnumerable<T> value)
    {
        AddTypedList(listKey, value, _defaultCacheDuration);
    }

    public void AddTypedList<T>(string listKey, IEnumerable<T> value, TimeSpan expireDate)
    {
        if (value == null) return;

        var jsonValue = value.ToRedisValue();

        _database.HashSet(listKey, new HashEntry[] { new HashEntry(listKey, JsonConvert.SerializeObject(value)) });
        _database.KeyExpire(listKey, expireDate);
    }

    public void AddTypedList<T>(string listKey, IEnumerable<T> value, int timeoutSeconds)
    {
        if (value == null) return;

        var jsonValue = value.ToRedisValue();

        var expireDate = TimeSpan.FromSeconds(timeoutSeconds);
        _database.HashSet(listKey, new HashEntry[] { new HashEntry(listKey, jsonValue) });
        _database.KeyExpire(listKey, expireDate);
    }

    public void AddTypedList<T>(string listKey, IEnumerable<T> value, int timeoutSeconds, bool isNewList = false)
    {
        AddTypedList(listKey, value, timeoutSeconds);
    }

    public IList<T> GetTypedList<T>(string listKey)
    {
        try
        {
            var result = new List<T>();
            var allHash = _database.HashGetAll(listKey);
            foreach (var item in allHash)
            {
                result = JsonConvert.DeserializeObject<List<T>>(item.Value);
            }
            return result;
        }
        catch (Exception exp)
        {
            _logger.LogError(listKey + " Error Message :" + exp.Message + " Error:" + exp.StackTrace);
        }

        return new List<T>();
    }

    public IList<T> GetTypedList<T>(string listKey, Func<T, bool> filterFunction)
    {
        try
        {
            var result = GetTypedList<T>(listKey).Where(filterFunction).ToList();
            return result;
        }
        catch (Exception exp)
        {
            _logger.LogError(listKey + " Error Message :" + exp.Message + " Error:" + exp.StackTrace);
        }

        return new List<T>();

    }

    public IList<T> GetTypedList<T>(string listKey, short page, int pageSize, out int totalItemCount)
    {
        totalItemCount = 0;
        try
        {
            int pageIndex = page - 1;

            var list = GetTypedList<T>(listKey);
            if (list.Any())
            {
                totalItemCount = list.Count();
                var x = list.Skip((page - 1) * pageSize).Take(pageSize).ToList();
                return x;
            }
        }
        catch (Exception exp)
        {
            _logger.LogError(listKey + " Error Message :" + exp.Message + " Error:" + exp.StackTrace);
        }
        return new List<T>();
    }

    public IList<T> GetTypedList<T>(string listKey, Func<T, bool> filterFunction, short page, int pageSize, out int totalItemCount)
    {
        totalItemCount = 0;
        try
        {
            var list = GetTypedList<T>(listKey).Where(filterFunction).ToList();
            if (list.Any())
            {
                totalItemCount = list.Count();
                var result = list.Skip((page - 1) * pageSize).Take(pageSize).ToList();
                return result;
            }
        }
        catch (Exception exp)
        {
            _logger.LogError(listKey + " Error Message :" + exp.Message + " Error:" + exp.StackTrace);
        }

        return new List<T>();
    }

    public IList<T> GetTypedOrderList<T>(string listKey, Func<T, object> expression, OrderByDirections direction)
    {
        try
        {
            var list = GetTypedList<T>(listKey);

            switch (direction)
            {
                case OrderByDirections.Asc:
                    return list.OrderBy(expression).ToList();
                case OrderByDirections.Desc:
                    return list.OrderByDescending(expression).ToList();
                default:
                    throw new ArgumentOutOfRangeException("direction");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(listKey + " Error Message :" + ex.Message + " Error:" + ex.StackTrace);
        }

        return new List<T>();
    }

    public IList<T> GetTypedOrderList<T>(string listKey, Func<T, object> expression, OrderByDirections direction, int topN)
    {
        try
        {
            var list = GetTypedList<T>(listKey);

            switch (direction)
            {
                case OrderByDirections.Asc:
                    return list.OrderBy(expression).Take(topN).ToList();
                case OrderByDirections.Desc:
                    return list.OrderByDescending(expression).Take(topN).ToList();
                default:
                    throw new ArgumentOutOfRangeException("direction");
            }

        }
        catch (Exception ex)
        {
            _logger.LogError(listKey + " Error Message :" + ex.Message + " Error:" + ex.StackTrace);
        }

        return new List<T>();
    }

    public void Update<T>(string listKey, T value, Func<T, bool> filterFunction)
    {
        try
        {
            var redisData = GetTypedList<T>(listKey);

            if (redisData.Any())
            {
                var itemList = redisData.Where(filterFunction);
                foreach (var item in itemList)
                {
                    redisData.Remove(item);
                }
                redisData.Add(value);
                AddTypedList(listKey, redisData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(listKey + " Error Message :" + ex.Message + " Error:" + ex.StackTrace);
        }
    }

    public void EnqueueToTypedList<T>(string key, IEnumerable<T> value)
    {
        try
        {
            if (value == null || !value.Any()) return;

            var redisData = GetTypedList<T>(key);

            if (redisData.Any())
            {
                foreach (var item in value)
                {
                    redisData.Add(item);
                }
                AddTypedList(key, redisData);
            }
            else
            {
                AddTypedList(key, value);
            }
        }
        catch (Exception exp)
        {
            _logger.LogError(key + " Error Message :" + exp.Message + " Error:" + exp.StackTrace);
        }

    }

    public int GetIndexes<T>(string listkey, T value, Predicate<T> filterFunction)
    {
        throw new NotImplementedException();
    }

    public void RemoveFromList<T>(string listKey, Func<T, bool> filterFunction)
    {
        throw new NotImplementedException();
    }

    public int GetIndexOfList<T>(string listKey, T item)
    {
        throw new NotImplementedException();
    }

    public void Store<T>(string hashId, Dictionary<string, T> mapValues)
    {
        var redisValue = mapValues.Select(
            pair => new HashEntry(pair.Key, JsonConvert.SerializeObject(pair.Value))).ToArray();

        _database.HashSet(hashId, redisValue);
    }

    public T GetValueFromHash<T>(string hashId, string key)
    {
        try
        {
            if (_database.HashExists(hashId, key))
            {
                var redisValue = _database.HashGet(hashId, key);
                return JsonConvert.DeserializeObject<T>(redisValue.ToString());
            }
        }
        catch (Exception exp)
        {
            _logger.LogError(key + " Error Message :" + exp.Message + " Error:" + exp.StackTrace); ;
        }

        return default;
    }

    public void RemoveFromHash<T>(string hashId)
    {
        throw new NotImplementedException();
    }

    public IEnumerable<KeyValuePair<string, T>> GetMultipleValueFromHash<T>(string hashId, string[] keys)
    {
        throw new NotImplementedException();
    }

    public bool IsSet(string key)
    {
        try
        {
            return ContainsKey(key);
        }
        catch (Exception exp)
        {
            _logger.LogError(key + " Error Message :" + exp.Message + " Error:" + exp.StackTrace);
        }

        return false;
    }

    public void Update<T>(string listkey, T value)
    {
        throw new NotImplementedException();
    }

    private List<HashEntry> ConvertToHashEntryList(object instance)
    {
        var propertiesInHashEntryList = new List<HashEntry>();
        foreach (var property in instance.GetType().GetProperties())
        {
            if (!property.Name.Equals("ObjectAdress"))
            {
                // This is just for an example
                propertiesInHashEntryList.Add(new HashEntry(property.Name, instance.GetType().GetProperty(property.Name).GetValue(instance).ToString()));
            }
            else
            {
                var subPropertyList = ConvertToHashEntryList(instance.GetType().GetProperty(property.Name).GetValue(instance));
                propertiesInHashEntryList.AddRange(subPropertyList);
            }
        }
        return propertiesInHashEntryList;
    }
}

public static class Extensions
{
    public static RedisValue[] ToRedisValues<T>(this IEnumerable<T> source)
    {
        if (source == null)
        {
            return new RedisValue[0];
        }

        return source.Select(item => (RedisValue)JsonConvert.SerializeObject(item)).ToArray();
    }

    public static T To<T>(this RedisValue redisValue)
    {
        return JsonConvert.DeserializeObject<T>(redisValue);
    }

    public static T ToList<T>(this RedisValue redisValue)
    {
        return JsonConvert.DeserializeObject<T>(redisValue);
    }

    public static string ToRedisValue(this object source)
    {
        return JsonConvert.SerializeObject(source);
    }

    public static T Deserialize<T>(byte[] stream)
    {
        if (stream == null || stream.Length == 0)
            return default;

        try
        {
            var json = Encoding.UTF8.GetString(stream);
            return JsonConvert.DeserializeObject<T>(json);
        }
        catch
        {
            return default;
        }
    }
}
