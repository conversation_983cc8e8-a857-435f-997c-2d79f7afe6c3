﻿namespace Bigpara.Domain.SummaryTypes;

public class SembolOzet
{
    public int SEMBOLID { get; set; }
    public string SEMBOL { get; set; }
    public DateTime TARIH { get; set; }
    public string ACIKLAMA { get; set; }
    public int YIL { get; set; }
    public int AY { get; set; }
    public int GUN { get; set; }
    public double SATIS { get; set; }
    public double KAPANIS1 { get; set; }
    public double KAPANIS2 { get; set; }
    public double KAPANIS { get; set; }
    public decimal HACIMLOT { get; set; }
    public double AORT { get; set; }
    public decimal HACIMTL { get; set; }
    public double YUKSEK { get; set; }
    public double DUSUK { get; set; }
    public double YUZDE { get; set; }
    public double YUZDEDEGISIM { get; set; }
    public double NET { get; set; }
    public double DUNKUKAPANIS { get; set; }
    public double DUN { get; set; }
    public int PIYASAID { get; set; }
    public bool AKTIF { get; set; }
    public double YUKSEK1 { get; set; }
    public double DUSUK1 { get; set; }
    public double YUKSEK2 { get; set; }
    public double DUSUK2 { get; set; }

    public decimal HACIMLOT1 { get; set; }
    public double AORT1 { get; set; }
    public decimal HACIMTL1 { get; set; }

    public decimal HACIMLOT2 { get; set; }
    public double AORT2 { get; set; }
    public decimal HACIMTL2 { get; set; }

    public double YUZDEDEGISIMS2 { get; set; }
    public double YUZDEDEGISIMS1 { get; set; }
}


public class SembolOzetList
{
    public List<SembolOzet> SembolOzets { get; set; }
    public int TotalCount { get; set; }

    public SembolOzetList()
    {
        SembolOzets = new List<SembolOzet>();
    }
}
