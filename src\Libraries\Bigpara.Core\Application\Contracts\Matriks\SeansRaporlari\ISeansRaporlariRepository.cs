﻿using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.StoredProcedureResults;

namespace Bigpara.Core.Application.Contracts.Matriks.SeansRaporlari;

public interface ISeansRaporlariRepository
{
    Task<int> CreateSeansRaporu(SeansRaporu seansRaporu);
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisseler();
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkBist0300EnCokDegerKazanaHisseler();
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkIslemHacmi();
    Task<SeansRaporuYuzeysel> GetSeansRaporu10DkBist050Endeks();
    Task<SeansRaporuYuzeysel> GetSeansRaporu10DkBist030Endeks();
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkEnCokIslemAdediSahipHisseler();
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu1DkHisseAktivitiBilgisi();
    Task<SeansRaporuYuzeysel> GetSeansRaporuBistAcilisKapanis();
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkTavandaIslemGorenHisseler();
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkTabandaIslemGorenHisseler();
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkDegerKazananDikkatCeken();
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkDegerKaybiDikkatCeken();
    Task<List<SeansRaporuBistHisselerDurumIstatistik>> GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleri();
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkEnCokDegerKaybeden();
    Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkEnCokDegerKazanan();
    Task<SeansRaporuDoviz> GetSeansRaporu15DkDoviz(string sembol);
}