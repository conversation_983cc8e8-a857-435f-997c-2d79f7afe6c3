﻿using System.Text;
using System.Text.Json;

namespace Bigpara.Service.Hangfire.Infrastructre.Publishers
{
 
    public class HttpPublisher : IPublisher
    {
        private readonly HttpClient _client;
        private readonly ILogger<HttpPublisher> _logger;
        public HttpPublisher(IHttpClientFactory httpClientFactory, ILogger<HttpPublisher> logger)
        {
            _client = httpClientFactory.CreateClient("BigparaSignalrService");
            _logger = logger;
        }

        public Task PublishAllSymbolsAsync(List<Dictionary<string, string>> data)
        {
            throw new NotImplementedException();
        }

        public async Task PublishSymbolDataAsync(string symbol, Dictionary<string, string> data)
        {
            try
            {
                var apiUrl = "api/trade";
                var json = JsonSerializer.Serialize(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                ;

                var response = await _client.PostAsync(apiUrl, content);

                if (response.IsSuccessStatusCode)
                {
                    await response.Content.ReadAsStringAsync();
                }
                else
                {
                    _logger.LogError($"Hata: {response.StatusCode}, {await response.Content.ReadAsStringAsync()}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"İstek gönderilirken hata oluştu: {ex.Message}");
            }

            await Task.CompletedTask;
        }

        public Task PublishSymbolDetailAsync(string symbol, Dictionary<string, string> detail)
        {
            throw new NotImplementedException();
        }

        public Task PublishSymbolsByTypeAsync(string type, List<Dictionary<string, string>> symbols)
        {
            throw new NotImplementedException();
        }
    }
}
