﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Bigpara.Persistence.SqlServer.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddCustomSqlServer<ITContext,TContext>(
        this IServiceCollection services,
        IConfiguration configuration,
        string connectionStringName)
        where TContext : DbContext
        where ITContext : IDbContext
        
    {
        services.AddScoped(typeof(ITContext), typeof(TContext));
        services.AddDbContext<TContext>(options =>
        {
            options.UseSqlServer(configuration.GetConnectionString(connectionStringName));
            options.LogTo(Console.WriteLine, LogLevel.Information)
                   .EnableSensitiveDataLogging()
                   .EnableDetailedErrors();
        });
        
        

        return services;
    }
}
