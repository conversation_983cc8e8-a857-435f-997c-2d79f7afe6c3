﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructre.Data;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class SectorService : ISectorService
{
    private readonly ILogger<SectorService> _logger;
    private readonly IStoredProcedureParameterService _storedProcedureService;

    public SectorService
    (
        IStoredProcedureParameterService storedProcedureService,
        ILogger<SectorService> logger
    )
    {
        _storedProcedureService = storedProcedureService;
        _logger = logger;
    }

    public async Task Change(SektorlerDto sektorlerDto)
    {
        try
        {
            sektorlerDto.StrAd = TextHelper.RemoveSpecialCharacters(sektorlerDto?.StrAd, "\"", "\\");
            var tanimSektorlerParameters = _storedProcedureService.CreateTanimSektorlerStoredProcedureParameters(sektorlerDto);
            await _storedProcedureService.ExecuteStoredProcedureAsync("sp_foreks_TanimSektorler", tanimSektorlerParameters, sektorlerDto?.StrAd);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Sektor işleme hatası: {sektorlerDto.StrAd}");
        }
    }
}