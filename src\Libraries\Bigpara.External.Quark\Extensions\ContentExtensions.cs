﻿using Bigpara.Core.Dtos.Quark;
using Quark.Models.Entities;

namespace Bigpara.External.Quark.Extensions
{
    public static class ContentExtensions
    {
        public static NewsDto ToNewsDto(this SuperContent content)
        {
            if (content == null) throw new ArgumentNullException(nameof(content));

            var imageUrl = GetImageUrl(content);

            return new NewsDto
            {
                Id = content._Id,
                Title = content.Title,
                Category = content.Ancestors?.LastOrDefault()?.Title,
                CreateDate = content.StartDate.FormatDate(),
                ModifiedDate = (content.ModifiedDate ?? content.StartDate).FormatDate(),
                Description = content.Description,
                Url = content.Url,
                Image = imageUrl,
                ContentType = content.ContentType,
                Path = content.Path
            };
        }

        private static string GetImageUrl(SuperContent content)
        {
            if (content.Files == null || !content.Files.Any())
                return "https://image.hurimg.com/i/hurriyet/100/0x0/57bbf91967b0a930747c6900.jpg";

            var fileId = content.Files.FirstOrDefault()?._Id;
            return $"https://image.hurimg.com/i/hurriyet/100/0x0/{fileId}.jpg";
        }

       
    }
}
