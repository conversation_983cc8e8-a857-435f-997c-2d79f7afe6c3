﻿using Microsoft.Extensions.Options;
using System.Text;
using System.Text.Json;

namespace Bigpara.Notifications.Teams;

public class TeamsNotificationService : ITeamsNotificationService
{
    private readonly TeamsConfiguration _teamsConfig;
    private readonly HttpClient _httpClient;

    public TeamsNotificationService(IOptions<TeamsConfiguration> teamsConfig, HttpClient httpClient)
    {
        _teamsConfig = teamsConfig.Value;
        _httpClient = httpClient;
    }

    public async Task SendErrorNotificationAsync(string jobName, string errorMessage)
    {
        var payload = new
        {
            type = "message",
            attachments = new[]
            {
            new
            {
                contentType = "application/vnd.microsoft.card.adaptive",
                content = new
                {
                    schema = "http://adaptivecards.io/schemas/adaptive-card.json",
                    type = "AdaptiveCard",
                    version = "1.2",
                    body = new object[]
                    {
                        new
                        {
                            type = "TextBlock",
                            text = $"Environment: {_teamsConfig.Environment}, 🚨 {jobName} HATASI TESPİT EDİLDİ!",
                            weight = "Bolder",
                            size = "Large",
                            color = "Attention",
                            wrap = true,
                            horizontalAlignment = "Center"
                        },
                        new
                        {
                            type = "TextBlock",
                            text = errorMessage,
                            wrap = true,
                            separator = true,
                            spacing = "Medium",
                            color = "Attention",
                            size = "Medium"
                        },
                        new
                        {
                            type = "TextBlock",
                            text = $"🕒 {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                            isSubtle = true,
                            size = "Small",
                            wrap = true,
                            spacing = "Small"
                        },
                        new
                        {
                            type = "TextBlock",
                            text = "<at>Everyone</at>",
                            wrap = true,
                            weight = "Bolder",
                            size = "Medium",
                            color = "Accent",
                            spacing = "Medium"
                        }
                    },
                    actions = new object[]
                    {
                        new
                        {
                            type = "Action.OpenUrl",
                            title = "Sistemi Kontrol Et",
                            url = "https://master-hangfire-bigpara-test.hurriyet.com.tr/hangfire/recurring"
                        },
                        new
                        {
                            type = "Action.OpenUrl",
                            title = "Kibana Loglarını İncele",
                            url = "http://kibana.log.hurriyet.com.tr/goto/02872b23aba01b35627a4378200435a4"
                        },
                        new
                        {
                            type = "Action.OpenUrl",
                            title = "Pipeline Hatalarını Gör",
                            url = "https://dev.azure.com/DemirorenMedya/Bigpara/_build?definitionId=1407"
                        }
                    },
                    msteams = new
                    {
                        width = "Full",
                        entities = new object[]
                        {
                            new
                            {
                                type = "mention",
                                text = "<at>Everyone</at>",
                                mentioned = new
                                {
                                    id = "19:07b682c9e2074786ad578d484e6ef588@thread.v2",
                                    name = "Everyone"
                                }
                            }
                        }
                    }
                }
            }
        }
        };

        await SendTeamsNotificationAsync(payload);
    }


    public async Task SendInfoNotificationAsync(string jobName, string infoMessage)
    {
        var payload = new
        {
            type = "message",
            attachments = new[]
            {
            new
            {
                contentType = "application/vnd.microsoft.card.adaptive",
                content = new
                {
                    schema = "http://adaptivecards.io/schemas/adaptive-card.json",
                    type = "AdaptiveCard",
                    version = "1.2",
                    body = new object[]
                    {
                        new
                        {
                            type = "TextBlock",
                            text = $"Environment: {_teamsConfig.Environment}, 🔔 {jobName} Başladı!",
                            weight = "Bolder",
                            size = "Large",
                            color = "Accent",
                            wrap = true
                        },
                        new
                        {
                            type = "TextBlock",
                            text = infoMessage,
                            wrap = true,
                            separator = true,
                            spacing = "Medium"
                        },
                        new
                        {
                            type = "TextBlock",
                            text = $"🕒 {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                            isSubtle = true,
                            size = "Small",
                            wrap = true,
                            spacing = "Small"
                        },
                        new
                        {
                            type = "TextBlock",
                            text = "<at>Everyone</at>",
                            weight = "Bolder",
                            size = "Large",
                            color = "Accent",
                            wrap = true
                        }
                    },
                    actions = new object[]
                    {
                        new
                        {
                            type = "Action.OpenUrl",
                            title = "Sistemi Kontrol Et",
                            url = "https://master-hangfire-bigpara-test.hurriyet.com.tr/hangfire/recurring"
                        },
                        new
                        {
                            type = "Action.OpenUrl",
                            title = "Kibana Log",
                            url = "http://kibana.log.hurriyet.com.tr/goto/02872b23aba01b35627a4378200435a4"
                        },
                        new
                        {
                            type = "Action.OpenUrl",
                            title = "Azure Pipeline",
                            url = "https://dev.azure.com/DemirorenMedya/Bigpara/_build?definitionId=1407"
                        }
                    },
                    msteams = new
                    {
                        width = "Full",
                        entities = new object[]
                        {
                            new
                            {
                                type = "mention",
                                text = "<at>Everyone</at>",
                                mentioned = new
                                {
                                    id = "19:07b682c9e2074786ad578d484e6ef588@thread.v2",
                                    name = "Everyone"
                                }
                            }
                        }
                    }
                }
            }
        }
        };

        await SendTeamsNotificationAsync(payload);
    }



    private async Task<bool> SendTeamsNotificationAsync(object payload)
    {
        try
        {
            string json = JsonSerializer.Serialize(payload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(_teamsConfig.WebhookUrl, content);
            response.EnsureSuccessStatusCode();

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Bildirim gönderilirken hata oluştu: {ex.Message}");
            return false;
        }
    }
}
