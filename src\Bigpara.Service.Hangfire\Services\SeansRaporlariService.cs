﻿using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Core.Application.Contracts.Matriks.SeansRaporlari;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.StoredProcedureResults;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class SeansRaporlariService : ISeansRaporlariService
{
    private readonly ISeansRaporlariRepository _seansRaporlariRepository;
    private readonly IRedisCacheService _redisCacheService;

    public SeansRaporlariService
    (
        ISeansRaporlariRepository seansRaporlariRepository,
        IRedisCacheService redisCacheService
    )
    {
        _seansRaporlariRepository = seansRaporlariRepository;
        _redisCacheService = redisCacheService;
    }

    public async Task<int> CreateSeansRaporu(SeansRaporu seansRaporu)
    {
        return await _seansRaporlariRepository.CreateSeansRaporu(seansRaporu);
    }

    public async Task<SeansRaporuYuzeysel> GetSeansRaporu10DkBist030Endeks()
    {
        return await _seansRaporlariRepository.GetSeansRaporu10DkBist030Endeks();
    }

    public async Task<SeansRaporuYuzeysel> GetSeansRaporu10DkBist050Endeks()
    {
        return await _seansRaporlariRepository.GetSeansRaporu10DkBist050Endeks();
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkEnCokDegerKaybeden()
    {
        return await _seansRaporlariRepository.GetSeansRaporu10DkEnCokDegerKaybeden();
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkEnCokDegerKazanan()
    {
        return await _seansRaporlariRepository.GetSeansRaporu10DkEnCokDegerKazanan();
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkEnCokIslemAdediSahipHisseler()
    {
        return await _seansRaporlariRepository.GetSeansRaporu10DkEnCokIslemAdediSahipHisseler();
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu10DkIslemHacmi()
    {
        return await _seansRaporlariRepository.GetSeansRaporu10DkIslemHacmi();
    }

    public async Task<List<SeansRaporuBistHisselerDurumIstatistik>> GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleri()
    {
        return await _seansRaporlariRepository.GetSeansRaporu15DkArtanAzalanDegismeyenHisseAdetleri();
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisseler()
    {
        return await _seansRaporlariRepository.GetSeansRaporu15DkBist0300EnCokDegerKaybedenHisseler();
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkBist0300EnCokDegerKazanaHisseler()
    {
        return await _seansRaporlariRepository.GetSeansRaporu15DkBist0300EnCokDegerKazanaHisseler();
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkDegerKaybiDikkatCeken()
    {
        return await _seansRaporlariRepository.GetSeansRaporu15DkDegerKaybiDikkatCeken();
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkDegerKazananDikkatCeken()
    {
        return await _seansRaporlariRepository.GetSeansRaporu15DkDegerKazananDikkatCeken();
    }

    public async Task<SeansRaporuDoviz> GetSeansRaporu15DkDoviz(string sembol)
    {
        return await _seansRaporlariRepository.GetSeansRaporu15DkDoviz(sembol);
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkTabandaIslemGorenHisseler()
    {
        return await _seansRaporlariRepository.GetSeansRaporu15DkTabandaIslemGorenHisseler();
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu15DkTavandaIslemGorenHisseler()
    {
        return await _seansRaporlariRepository.GetSeansRaporu15DkTavandaIslemGorenHisseler();
    }

    public async Task<List<SeansRaporuYuzeysel>> GetSeansRaporu1DkHisseAktivitiBilgisi()
    {
        return await _seansRaporlariRepository.GetSeansRaporu1DkHisseAktivitiBilgisi();
    }

    public async Task<SeansRaporuYuzeysel> GetSeansRaporuBistAcilisKapanis()
    {
        return await _seansRaporlariRepository.GetSeansRaporuBistAcilisKapanis();
    }

    public async Task CreateAndCacheSeansRaporu(SeansRaporu seansRaporu)
    {
        await _seansRaporlariRepository.CreateSeansRaporu(seansRaporu);

        if (_redisCacheService.ContainsKey(CacheKeys.BIGPARA_BORSA_SEANS_RAPORU_ALL_KEY))
        {
            _redisCacheService.EnqueueToTypedList<SeansRaporu>(CacheKeys.BIGPARA_BORSA_SEANS_RAPORU_ALL_KEY, new List<SeansRaporu> { seansRaporu });
        }
    }

    public bool IsActive()
    {
        var time = TimeSpan.Parse(DateTime.Now.ToString("HH:mm"));
        return (time >= TimeSpan.Parse("09:30:00") && time < TimeSpan.Parse("12:30:00")) ||
                (time >= TimeSpan.Parse("14:00:00") && time < TimeSpan.Parse("17:30:00"));
    }
}
