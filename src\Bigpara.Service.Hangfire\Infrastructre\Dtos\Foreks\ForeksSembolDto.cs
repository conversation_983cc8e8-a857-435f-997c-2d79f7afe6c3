﻿using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;

namespace Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;

public class ForeksSembolDto
{
    [JsonProperty("code")]
    public string? Sembol { get; set; }

    [JsonProperty("_id")]
    public string? SocketId { get; set; }


    [JsonProperty("securityDesc")]
    public string? Aciklama { get; set; }

    //TODO : Bu alan i<PERSON> dö<PERSON> yapacaklar.
    //[JsonProperty("ISIN")]
    public byte? PiyasaId { get; set; }


    [JsonProperty("index")]
    public List<string>? Endeks { get; set; }

    [JsonProperty("indexWeight")]
    public Dictionary<string, Dictionary<string, decimal>>? IndexWeight { get; set; }

    [NotMapped]
    public string? StrEndeks { get; set; }

    [JsonProperty("subMarket")]

    public string? ImkbPazarKodString { get; set; }

    [JsonProperty("security")]
    public string? ImkbHisseTipString { get; set; }

    [JsonIgnore]
    public char? ImkbHisseTip
    {
        get
        {
            if (string.IsNullOrEmpty(ImkbHisseTipString) || ImkbHisseTipString.Length != 1)
                return null;

            return ImkbHisseTipString[0];
        }
        set =>
            ImkbHisseTipString = value?.ToString();
    }

    [JsonProperty("precision")]
    public byte? OndalikBasamak { get; set; }

    //TODO : Doğru değer iletilince setlenecek.
    //[JsonProperty("subMarket")]
    [NotMapped]
    public char? HisseGrubu { get; set; }


    [JsonProperty("grossSettl")]

    public char? BruttTakas { get; set; } = '0';

    [JsonIgnore]
    public char? ImkbPazarKod
    {
        get
        {
            if (string.IsNullOrEmpty(ImkbPazarKodString) || ImkbPazarKodString.Length != 1)
                return null;

            return ImkbPazarKodString[0];
        }
        set =>
            ImkbPazarKodString = value?.ToString();
    }

    public string? ForeksKod { get; set; }

    [JsonProperty("sector")]
    public string? Sector { get; set; }

    [JsonProperty("legacyCode")]
    public string? ForeksKodYeni { get; set; }


    [JsonProperty("status")]
    public string? StatusString { get; set; }

    public bool Aktif { get; set; } = false;

    [JsonProperty("pttRow")]
    public string? PttRow { get; set; }

    [JsonProperty("market")]
    public string? Market { get; set; }

    [JsonProperty("marketSector")]
    public string? MarketSector { get; set; }

    [JsonProperty("securityType")]
    public string? SecurityType { get; set; }

    [JsonIgnore]
    public string? StrPiyasa { get; set; }

    [JsonIgnore]
    public string? SektorId { get; set; }

    [JsonProperty("domain")]
    public string? Domain { get; set; }

    [JsonProperty("exchange")]
    public string Exchange { get; set; } = "";

    [JsonProperty("board")]
    public string StrBoard { get; set; }
}
