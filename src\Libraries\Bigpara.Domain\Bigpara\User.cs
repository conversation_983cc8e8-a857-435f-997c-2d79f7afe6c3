﻿using System.ComponentModel.DataAnnotations;

namespace Bigpara.Domain.Bigpara;

public class User
{
    public int Id { get; set; }
    [Required(ErrorMessage = "<PERSON><PERSON> alan bo<PERSON> bırakılamaz")]
    public string HurPassId { get; set; }

    public UserType UserType { get; set; }
    //[Required]
    public string FirstName { get; set; }
    //[Required]
    public string LastName { get; set; }
    public string Email { get; set; }
    public DateTime AddDate { get; set; }
    public bool IsDeleted { get; set; }
    public bool IsActive { get; set; }
    public DateTime LastLoginDate { get; set; }
    public bool IsVerified { get; set; }
    public bool IsSecure { get; set; }
    public DateTime LastActivityDate { get; set; }
    public Nullable<DateTime> BirthDate { get; set; }
    public byte UserTypeId { get; set; }
    public string Address { get; set; }
    public int ZipCode { get; set; }
    public int Gender { get; set; }
    public int CityId { get; set; }
    public int CountryId { get; set; }
    public string CountryName { get; set; }
    public string FBProfile { get; set; }
    public string LIProfile { get; set; }
    public string TWProfile { get; set; }
    public bool IsHurPassData { get; set; }
    public string TckNumber { get; set; }

    public string TaxNumber { get; set; }

    public string AddressType { get; set; }
    public string CompanyName { get; set; }
    public string TaxAdministration { get; set; }

    public string Password { get; set; }

    public string FullName => $"{FirstName} {LastName}";

    public List<UserSembol> UserSembolList { get; set; }

}
