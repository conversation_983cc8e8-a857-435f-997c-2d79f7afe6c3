﻿using Bigpara.Core.Application.Contracts.Matriks.Borsa;
using Bigpara.Domain.Matriks;
using Microsoft.Data.SqlClient;

namespace Bigpara.Persistence.Matriks.Borsa;

public class OranlarSektorelRepository : IOranlarSektorelRepository
{

    private readonly IMatriksDbContext _matriksDbContext;
    public OranlarSektorelRepository(IMatriksDbContext matriksDbContext)
    {
        _matriksDbContext = matriksDbContext;
    }
    public virtual async Task<List<OranlarSektorel>> GetOranlarSektorels(string sembol)
    {
        var parameters = new List<SqlParameter>();
        parameters.Add(new SqlParameter("sembol", sembol));
       
        return await _matriksDbContext.ExecuteStoredProcedureAsync<OranlarSektorel>("bp.pGetHisseMaliOranlar", parameters.ToArray());

    }
    public async Task<List<OranlarSektorel>> GetSektorelMaliOranlar()
    {
        return await _matriksDbContext.ExecuteStoredProcedureAsync<OranlarSektorel>("bp.pGetMaliOranlar");

    }

    public async Task<int> InsertOranSektorel(OranlarSektorel oranlarSektorel)
    {
        var parameters = new List<SqlParameter>() ;
        parameters.Add(new SqlParameter("Id", oranlarSektorel.Id));
        parameters.Add(new SqlParameter("Sektor_ID", oranlarSektorel.Sektor_ID));
        parameters.Add(new SqlParameter("Senet", oranlarSektorel.Senet));
        parameters.Add(new SqlParameter("Donem", oranlarSektorel.Donem));
        parameters.Add(new SqlParameter("F_K", oranlarSektorel.F_K));
        parameters.Add(new SqlParameter("FKOrant", oranlarSektorel.FKOrant));
        parameters.Add(new SqlParameter("PD_DD", oranlarSektorel.PD_DD));
        parameters.Add(new SqlParameter("NetKar_TopAktif", oranlarSektorel.NetKar_TopAktif));
        parameters.Add(new SqlParameter("NetKar_Ozsermaye", oranlarSektorel.NetKar_Ozsermaye));
        parameters.Add(new SqlParameter("PD_NetSatis", oranlarSektorel.PD_NetSatis));
        parameters.Add(new SqlParameter("PD_EFaalKar", oranlarSektorel.PD_EFaalKar));
        parameters.Add(new SqlParameter("NetKar_NetSatis", oranlarSektorel.NetKar_NetSatis));
        parameters.Add(new SqlParameter("EFaalKar_NetSatis", oranlarSektorel.EFaalKar_NetSatis));
        parameters.Add(new SqlParameter("Cari_Oran", oranlarSektorel.Cari_Oran));
        parameters.Add(new SqlParameter("EFaalKar_KVBorc", oranlarSektorel.EFaalKar_KVBorc));
        parameters.Add(new SqlParameter("KVBorc_TopAktif", oranlarSektorel.KVBorc_TopAktif));
        parameters.Add(new SqlParameter("KVBor_TopBorc", oranlarSektorel.KVBor_TopBorc));
        parameters.Add(new SqlParameter("UpdatedDateTime", oranlarSektorel.UpdatedDateTime));

        return await _matriksDbContext.ExecuteNonQueryAsync("bp.pInsertOranlarSektorel", parameters.ToArray());
      
    }

    public async Task<int> DeleteOranSektorel()
    {
       return await _matriksDbContext.ExecuteNonQueryAsync("bp.pDeleteOranlarSektorel");

    }

    public virtual async Task<List<OranlarSektorel>> GetOranlarSektorelId(int id)
    {
        var parameters = new List<SqlParameter>();
        parameters.Add(new SqlParameter("Id", id));

        return await _matriksDbContext.ExecuteStoredProcedureAsync<OranlarSektorel>("bp.pGetHisseMaliOranlarById", parameters.ToArray());

    }
}
