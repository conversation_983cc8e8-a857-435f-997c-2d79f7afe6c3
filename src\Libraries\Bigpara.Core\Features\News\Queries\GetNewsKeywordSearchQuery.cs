﻿using Bigpara.Application.Features.News.Queries.ViewModels;
using Bigpara.Core.Application.Contracts.Cms;
using MediatR;

namespace Bigpara.Application.Features.News.Queries;

#region Query
public class GetNewsKeywordSearchQuery : IRequest<NewsSearchQueryViewModel>
{
    public string Keyword { get; set; }
    public int Size { get; set; }
}

#endregion

#region Handler
public class GetNewsKeywordSearchQueryHandler : IRequestHandler<GetNewsKeywordSearchQuery, NewsSearchQueryViewModel>
{
    private readonly INewsSearchRepository _searchRepository;

    public GetNewsKeywordSearchQueryHandler(INewsSearchRepository searchRepository)
    {
        _searchRepository = searchRepository;
    }

    public async Task<NewsSearchQueryViewModel> Handle(GetNewsKeywordSearchQuery request, CancellationToken cancellationToken)
    {

        var news = await _searchRepository.Search(request.Keyword, pageSize: request.Size);

        NewsSearchQueryViewModel response = new NewsSearchQueryViewModel();
        response.Data = new
        {
            news = news.Data,
            pageIndex = news.PageIndex,
            pageSize = news.PageSize
        };

        return response;

    }
}

#endregion

