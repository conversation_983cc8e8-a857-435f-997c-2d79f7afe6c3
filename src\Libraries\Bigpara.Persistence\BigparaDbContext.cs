﻿using Bigpara.Domain;
using Bigpara.Domain.Bigpara;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Data.Common;
using System.Reflection;

namespace Bigpara.Persistence;

public interface IBigparaDbContext : IDbContext
{
    public DbSet<Semboller> Semboller { get; set; }
    public DbSet<UserSembol> UserSembols { get; set; }

    // Generic command execution methods
    Task<List<T>> ExecuteStoredProcedureAsync<T>(string procedureName, params DbParameter[] parameters) where T : class, new();
    Task<List<T>> ExecuteQueryAsync<T>(string sql, params DbParameter[] parameters) where T : class, new();
    Task<int> ExecuteNonQueryAsync(string sql, params DbParameter[] parameters);
    Task<T> ExecuteScalarAsync<T>(string sql, params DbParameter[] parameters);
    Task<DataTable> ExecuteDataTableAsync(string sql, params DbParameter[] parameters);
    Task<T> ExecuteQuerySingleAsync<T>(string sql, params DbParameter[] parameters) where T : class, new();
    Task<T> ExecuteStoredProcedureSingleAsync<T>(string procedureName, params DbParameter[] parameters) where T : class, new();
}

public class BigparaDbContext : DbContext, IBigparaDbContext
{
    public BigparaDbContext(DbContextOptions<BigparaDbContext> options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Semboller>()
                    .HasKey(s => s.SembolId);


        base.OnModelCreating(modelBuilder);
    }

    public DbSet<Semboller> Semboller { get; set; }
    public DbSet<UserSembol> UserSembols { get; set; }

    #region Generic Command Execution Methods

    /// <summary>
    /// Stored procedure'ü çalıştırır ve sonuçları generic tip olarak döner
    /// </summary>
    public async Task<List<T>> ExecuteStoredProcedureAsync<T>(string procedureName, params DbParameter[] parameters) where T : class, new()
    {
        var connection = Database.GetDbConnection();
        var wasClosed = connection.State == ConnectionState.Closed;

        try
        {
            if (wasClosed)
                await connection.OpenAsync();

            await using var command = connection.CreateCommand();
            command.CommandText = procedureName;
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null)
                command.Parameters.AddRange(parameters);

            await using var reader = await command.ExecuteReaderAsync();

            return await MapDataReaderToList<T>(reader);
        }
        finally
        {
            if (wasClosed && connection.State != ConnectionState.Closed)
                await connection.CloseAsync();
        }
    }

    /// <summary>
    /// Stored procedure'ü çalıştırır ve sonuçları generic tip olarak döner
    /// </summary>
    public async Task<T> ExecuteStoredProcedureSingleAsync<T>(string procedureName, params DbParameter[] parameters) where T : class, new()
    {
        var connection = Database.GetDbConnection();
        var wasClosed = connection.State == ConnectionState.Closed;

        try
        {
            if (wasClosed)
                await connection.OpenAsync();

            await using var command = connection.CreateCommand();
            command.CommandText = procedureName;
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null && parameters?.Length > 0)
                command.Parameters.AddRange(parameters);

            await using var reader = await command.ExecuteReaderAsync();

            return (await MapDataReaderToList<T>(reader))?.FirstOrDefault();
        }
        finally
        {
            if (wasClosed && connection.State != ConnectionState.Closed)
                await connection.CloseAsync();
        }
    }

    /// <summary>
    /// SQL sorgusu çalıştırır ve sonuçları generic tip olarak döner
    /// </summary>
    public async Task<List<T>> ExecuteQueryAsync<T>(string sql, params DbParameter[] parameters) where T : class, new()
    {
        var connection = Database.GetDbConnection();
        var wasClosed = connection.State == ConnectionState.Closed;

        try
        {
            if (wasClosed)
                await connection.OpenAsync();

            await using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandType = CommandType.Text;

            if (parameters != null && parameters?.Length > 0)
                command.Parameters.AddRange(parameters);

            await using var reader = await command.ExecuteReaderAsync();
            return await MapDataReaderToList<T>(reader);
        }
        finally
        {
            if (wasClosed && connection.State != ConnectionState.Closed)
                await connection.CloseAsync();
        }
    }



    /// <summary>
    /// SQL sorgusu çalıştırır ve sonuçları generic tip olarak döner
    /// </summary>
    public async Task<T> ExecuteQuerySingleAsync<T>(string sql, params DbParameter[] parameters) where T : class, new()
    {
        var connection = Database.GetDbConnection();
        var wasClosed = connection.State == ConnectionState.Closed;

        try
        {
            if (wasClosed)
                await connection.OpenAsync();

            await using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null && parameters?.Length > 0)
                command.Parameters.AddRange(parameters);

            await using var reader = await command.ExecuteReaderAsync();
            var data = await MapDataReaderToList<T>(reader);

            return data?.FirstOrDefault();
        }
        finally
        {
            if (wasClosed && connection.State != ConnectionState.Closed)
                await connection.CloseAsync();
        }
    }

    /// <summary>
    /// INSERT, UPDATE, DELETE gibi işlemler için kullanılır
    /// </summary>
    public async Task<int> ExecuteNonQueryAsync(string sql, params DbParameter[] parameters)
    {
        var connection = Database.GetDbConnection();
        var wasClosed = connection.State == ConnectionState.Closed;

        try
        {
            if (wasClosed)
                await connection.OpenAsync();

            await using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null && parameters?.Length > 0)
                command.Parameters.AddRange(parameters);

            return await command.ExecuteNonQueryAsync();
        }
        finally
        {
            if (wasClosed && connection.State != ConnectionState.Closed)
                await connection.CloseAsync();
        }
    }

    /// <summary>
    /// Tek bir değer dönen sorgular için kullanılır
    /// </summary>
    public async Task<T> ExecuteScalarAsync<T>(string sql, params DbParameter[] parameters)
    {
        var connection = Database.GetDbConnection();
        var wasClosed = connection.State == ConnectionState.Closed;

        try
        {
            if (wasClosed)
                await connection.OpenAsync();

            await using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null && parameters?.Length > 0)
                command.Parameters.AddRange(parameters);

            var result = await command.ExecuteScalarAsync();

            if (result == null || result == DBNull.Value)
                return default(T);

            return (T)Convert.ChangeType(result, typeof(T));
        }
        finally
        {
            if (wasClosed && connection.State != ConnectionState.Closed)
                await connection.CloseAsync();
        }
    }

    /// <summary>
    /// DataTable olarak sonuç döner
    /// </summary>
    public async Task<DataTable> ExecuteDataTableAsync(string sql, params DbParameter[] parameters)
    {
        var connection = Database.GetDbConnection();
        var wasClosed = connection.State == ConnectionState.Closed;

        try
        {
            if (wasClosed)
                await connection.OpenAsync();

            await using var command = connection.CreateCommand();
            command.CommandText = sql;
            command.CommandType = CommandType.StoredProcedure;

            if (parameters != null && parameters?.Length > 0)
                command.Parameters.AddRange(parameters);

            await using var reader = await command.ExecuteReaderAsync();
            var dataTable = new DataTable();
            dataTable.Load(reader);

            return dataTable;
        }
        finally
        {
            if (wasClosed && connection.State != ConnectionState.Closed)
                await connection.CloseAsync();
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// DataReader'dan generic liste oluşturur
    /// </summary>
    private async Task<List<T>> MapDataReaderToList<T>(DbDataReader reader) where T : class, new()
    {
        var list = new List<T>();
        var properties = typeof(T).GetProperties().Where(p => p.CanWrite).ToArray();

        while (await reader.ReadAsync())
        {
            var item = new T();

            foreach (var property in properties)
            {
                try
                {
                    // Column name'i ColumnAttribute varsa onunla, yoksa property name ile eşleştir
                    var columnName = property.GetCustomAttribute<ColumnAttribute>()?.Name ?? property.Name;

                    if (HasColumn(reader, columnName))
                    {
                        var value = reader[columnName];
                        if (value != null && value != DBNull.Value)
                        {
                            var propertyType = Nullable.GetUnderlyingType(property.PropertyType) ?? property.PropertyType;

                            object convertedValue;

                            if (propertyType.IsEnum)
                            {
                                var intValue = Convert.ToInt32(value);
                                convertedValue = Enum.ToObject(propertyType, intValue);
                            }
                            else
                            {
                                convertedValue = Convert.ChangeType(value, propertyType);
                            }

                            property.SetValue(item, convertedValue);
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log exception if needed
                    Console.WriteLine($"Error mapping property {property.Name}: {ex.Message}");
                }
            }

            list.Add(item);
        }

        return list;
    }


    /// <summary>
    /// DataReader'da belirtilen column'un var olup olmadığını kontrol eder
    /// </summary>
    private static bool HasColumn(DbDataReader reader, string columnName)
    {
        for (int i = 0; i < reader.FieldCount; i++)
        {
            if (reader.GetName(i).Equals(columnName, StringComparison.OrdinalIgnoreCase))
                return true;
        }
        return false;
    }


    /// <summary>
    /// SqlParameter oluşturmak için helper method
    /// </summary>
    public static SqlParameter CreateParameter(string name, object value, SqlDbType dbType = SqlDbType.NVarChar)
    {
        return new SqlParameter(name, dbType) { Value = value ?? DBNull.Value };
    }

    /// <summary>
    /// Birden fazla SqlParameter oluşturmak için helper method
    /// </summary>
    public static SqlParameter[] CreateParameters(params (string name, object value, SqlDbType dbType)[] parameters)
    {
        return parameters.Select(p => CreateParameter(p.name, p.value, p.dbType)).ToArray();
    }

    public IQueryable<TQuery> FromSql<TQuery>(string sql, params object[] parameters) where TQuery : class
    {
        throw new NotImplementedException();
    }

    #endregion
}
