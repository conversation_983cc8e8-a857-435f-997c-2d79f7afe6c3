﻿using Bigpara.Core.Application.Contracts.Matriks.Grafiks;
using Bigpara.Core.Application.Features.Grafiks.Queries.ViewModels;
using MediatR;

namespace Bigpara.Core.Application.Features.Grafiks.Queries;


#region Query
public class GetGrafik5DkQuery : IRequest<Grafik5DkViewModel>
{
    public string Symbols { get; set; }
    public int PerCount { get; set; }
}
#endregion

#region Handler
public class GetGrafik5DkQueryHandler : IRequestHandler<GetGrafik5DkQuery, Grafik5DkViewModel>
{
    private readonly IGrafikRepository _grafikGunlukRepository;
    public GetGrafik5DkQueryHandler(IGrafikRepository grafikGunlukRepository)
    {
        _grafikGunlukRepository = grafikGunlukRepository;
    }
    public async Task<Grafik5DkViewModel> Handle(GetGrafik5DkQuery request, CancellationToken cancellationToken)
    {
        var response = new Grafik5DkViewModel();
        var data = await _grafikGunlukRepository.GetGrafik5DkBySembolIds(request.Symbols, request.PerCount);

        if (data == null) { 
            response.Errors.Add("No data found for the provided symbols.");
            return response;
        }

        foreach (var item in request.Symbols.Split(','))
        {
            var grafikItem = new Grafik5DkItemViewModel();
            grafikItem.Current = data.OrderByDescending(p => p.TARIH).FirstOrDefault(p => p.SEMBOL == item);
            grafikItem.Chart = data.Where(p => p.SEMBOL == item).ToList();
            
            response.Data.Add(grafikItem);
        }

        return response;
    }
}

#endregion


