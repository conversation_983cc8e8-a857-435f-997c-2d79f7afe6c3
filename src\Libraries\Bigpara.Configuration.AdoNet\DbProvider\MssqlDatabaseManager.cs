﻿using Microsoft.Data.SqlClient;
using System.Data;
using System.Reflection;


namespace Bigpara.Configuration.AdoNet.DbProvider;

public enum SqlConnectionMode
{
    BIGPARA = 0,
    DBMATRIKS
}

public class DbQueryParameter
{
    public string Name { get; set; }
    public ParameterDirection Direction { get; set; }
    public object Value { get; set; }

    public DbQueryParameter(string paramName, object paramValue, ParameterDirection paramDirection = ParameterDirection.Input)
    {
        Name = paramName;
        Direction = paramDirection;
        Value = paramValue;
    }
}

public enum ExecuteType
{
    ExecuteReader,
    ExecuteNonQuery,
    ExecuteScalar
}

public class MssqlDatabaseManager
{
    private string ConnectionString
    {
        get
        {
            string connection;
            switch (ConnectionMode)
            {
                //TODO: Connection stringleri dinamik setlenecek.
                case SqlConnectionMode.BIGPARA:

                    connection = "Server=***************;Database=TestBigpara;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;";
                    break;
                case SqlConnectionMode.DBMATRIKS:
                    connection = "Server=***************;Database=DBMATRIKSv2;User Id=*****;Password=*****;Encrypt=True;TrustServerCertificate=True;MultipleActiveResultSets=True;Pooling=true;";
                    break;
                default:
                    connection = "";
                    break;
            }
            return connection;
        }
    }

    private MssqlDatabaseManager()
    {

    }

    public static MssqlDatabaseManager Instance()
    {
        return new MssqlDatabaseManager();
    }

    public SqlConnectionMode ConnectionMode { get; set; }

    private SqlConnection Connection { get; set; }

    private SqlCommand Command { get; set; }

    public List<DbQueryParameter> OutParameters { get; private set; }

    public int TotalRows { get; set; }

    private void Open(SqlConnectionMode connectionMode)
    {
        try
        {
            ConnectionMode = connectionMode;
            Connection = new SqlConnection(ConnectionString);

            Connection.Open();
        }
        catch (DataException)
        {

            Close();
        }
    }

    private void Close()
    {
        if (Connection != null)
        {
            Connection.Close();
        }
    }

    // executes stored procedure with DB parameteres if they are passed
    private object ExecuteProcedure(string procedureName, ExecuteType executeType, IEnumerable<DbQueryParameter> parameters)
    {
        object returnObject = null;

        if (Connection == null) return null;
        if (Connection.State != ConnectionState.Open) return null;

        Command = new SqlCommand()
        {
            CommandType = CommandType.StoredProcedure,
            Connection = Connection,
            CommandText = procedureName
        };

        // pass stored procedure parameters to command
        if (parameters != null)
        {
            Command.Parameters.Clear();

            foreach (DbQueryParameter dbParameter in parameters)
            {
                SqlParameter parameter = new SqlParameter();
                parameter.ParameterName = "@" + dbParameter.Name;
                parameter.Direction = dbParameter.Direction;
                if (dbParameter.Value is DateTime)
                    parameter.Value = Convert.ToDateTime(dbParameter.Value).ToOADate();
                else
                    parameter.Value = dbParameter.Value;
                Command.Parameters.Add(parameter);
            }
        }

        switch (executeType)
        {
            case ExecuteType.ExecuteReader:
                returnObject = Command.ExecuteReader();
                break;
            case ExecuteType.ExecuteNonQuery:
                returnObject = Command.ExecuteNonQuery();
                break;
            case ExecuteType.ExecuteScalar:
                returnObject = Command.ExecuteScalar();
                break;
            default:
                break;
        }

        return returnObject;
    }

    // updates output parameters from stored procedure
    private void UpdateOutParameters()
    {
        if (Command.Parameters.Count > 0)
        {
            OutParameters = new List<DbQueryParameter>();
            OutParameters.Clear();

            for (int i = 0; i < Command.Parameters.Count; i++)
            {
                if (Command.Parameters[i].Direction == ParameterDirection.Output)
                {
                    OutParameters.Add(new DbQueryParameter(Command.Parameters[i].ParameterName,
                                                      Command.Parameters[i].Value,
                                                      ParameterDirection.Output));
                }
            }
        }
    }

    // executes scalar query stored procedure without parameters
    public T ExecuteSingle<T>(string procedureName, SqlConnectionMode connectionMode) where T : new()
    {
        return ExecuteSingle<T>(procedureName, null, connectionMode);
    }

    // executes scalar query stored procedure and maps result to single object
    public T ExecuteSingle<T>(string procedureName, List<DbQueryParameter> parameters, SqlConnectionMode connectionMode) where T : new()
    {
        Open(connectionMode);
        IDataReader reader = (IDataReader)ExecuteProcedure(procedureName, ExecuteType.ExecuteReader, parameters);
        T tempObject = new T();

        if (reader.Read())
        {
            for (int i = 0; i < reader.FieldCount; i++)
            {
                SetObjectValue(reader.GetName(i), reader.GetValue(i), ref tempObject);
            }
        }

        reader.Close();

        UpdateOutParameters();

        Close();

        return tempObject;
    }

    // executes list query stored procedure without parameters
    public List<T> ExecuteList<T>(string procedureName, SqlConnectionMode connectionMode) where T : new()
    {
        return ExecuteList<T>(procedureName, null, connectionMode);
    }

    // executes list query stored procedure and maps result generic list of objects
    public List<T> ExecuteList<T>(string procedureName, List<DbQueryParameter> parameters, SqlConnectionMode connectionMode) where T : new()
    {
        List<T> listObjects = new List<T>();
        Open(connectionMode);
        IDataReader reader = (IDataReader)ExecuteProcedure(procedureName, ExecuteType.ExecuteReader, parameters);
        while (reader.Read())
        {
            T listItemObject = new T();

            for (int i = 0; i < reader.FieldCount; i++)
            {
                SetObjectValue(reader.GetName(i), reader.GetValue(i), ref listItemObject);
            }

            listObjects.Add(listItemObject);
        }

        reader.Close();

        UpdateOutParameters();

        Close();

        return listObjects;
    }

    // executes non query stored procedure with parameters
    public int ExecuteNonQuery(string procedureName, List<DbQueryParameter> parameters, SqlConnectionMode connectionMode)
    {
        int returnValue;

        Open(connectionMode);

        returnValue = (int)ExecuteProcedure(procedureName, ExecuteType.ExecuteNonQuery, parameters);

        UpdateOutParameters();

        Close();

        return returnValue;
    }

    private void SetObjectValue<T>(string name, object value, ref T itemObject)
    {
        if (value == DBNull.Value) return;
        Type typeofT = typeof(T);
        if (typeofT.IsClass)
        {
            if (name == "TotalRows")
            {
                TotalRows = Convert.ToInt32(value);
                return;
            }
            PropertyInfo propertyInfo = typeofT.GetProperty(name);
            if (propertyInfo == null) return;
            if (propertyInfo.PropertyType == typeof(DateTime))
                value = DateTime.FromOADate(Convert.ToDouble(value, System.Globalization.CultureInfo.InvariantCulture));

            propertyInfo.SetValue(itemObject, value, null);
        }
        else
        {
            if (typeof(T).GetInterface("IConvertible") == typeof(IConvertible))
                itemObject = (T)Convert.ChangeType(value, typeofT);
            else
                itemObject = (T)value;
        }
    }


}
