﻿using Bigpara.Domain.Matriks;

namespace Bigpara.Core.Application.Contracts.Matriks.Indikators;

public interface IIndikatorRepository
{
    Task<int> CreateOrUpdate(Indikatorler indikator);
    Task<Indikatorler> GetIndikatorSembolbyTarih(string sembol, DateTime dateTime);
    Task<List<Indikatorler>> GetIndikatorbyTarih(DateTime dateTime);
    Task<Indikatorler> GetIndikatorbySembol(string sembol);
    Task<List<Indikatorler>> GetIndikatorbyTarihSembol(string sembol, DateTime dateTime);
    Task<List<Indikatorler>> GetOtomatikTeknikYorumIndikatorSembolbyTarih(string sembol, DateTime dateTime);
}
