﻿using System.Text;

namespace Bigpara.Domain.HurPass;

public class SsoUser
{
    public string Id { get; set; }
    /// <summary>
    /// ilgili HurPass kullanıcısının hangi portaller üzerinde kaçtane bağlı hesbaı olduğunu göstermektedir
    /// </summary>
    public Dictionary<string, int> SubCounts { get; set; }
    /// <summary>
    /// 1   Kullanıcının hesabını etkinleştirdiğini gösteri
    /// 0   Kullancının hesabını henüz etkinleştirmediğini gösterir
    /// -1  Kullanıcının hesap durumunun belirsiz olduğunu gösterir
    /// </summary>
    public int IsAccountValid { get; set; }
    /// <summary>
    /// 1   Kullanıcının en az bir emailini doğruladığını gösterir
    /// 0   Kullanıcının profil sayfası üzerinde birincil emailini değiştirdiğini ve doğrulama beklendiğini gösterir
    /// -1  Kullanıcının hesap bilgililerinin belirsiz olduğunu gösterir        
    /// </summary>
    public int IsEmailValid { get; set; }
    public string CountryName { get; set; }
    public string CityName { get; set; }
    public string Email { get; set; }
    /// <summary>
    /// Kullanıcının HurPass/SSO benzersiz Id
    /// </summary>
    public string UserId { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string FullName { get; set; }
    public DateTime BirthDate { get; set; }
    public string FBprofile { get; set; }
    public string LIprofile { get; set; }
    public string TWprofile { get; set; }
    /// <summary>
    /// 1  Erkek 0  Kadın -1 Belirsiz
    /// </summary>
    public int Gender { get; set; }

    public string GenderAsText
    {
        get
        {
            switch (Gender)
            {
                case 0:
                    return "Kadın";
                case 1:
                    return "Erkek";
                default:
                    return "Belirtilmemiş";
            }
        }
    }
    /// <summary>
    /// Sso Api üzerinden sorgulanabilir olan  il plaka kodu
    /// </summary>
    public int CityId { get; set; }
    /// <summary>
    /// Sso Api üzerinden sorgulanabilir olan ilçe kodu
    /// </summary>
    public int TownId { get; set; }
    public int ParishId { get; set; }

    /// <summary>
    /// Our system user ID
    /// </summary>
    public int BigparaId { get; set; }

    public override string ToString()
    {
        var sb = new StringBuilder("SsoUser(");
        sb.Append("BigparaId: ");
        sb.Append(BigparaId);
        sb.Append(",FirstName: ");
        sb.Append(FirstName);
        sb.Append(",LastName: ");
        sb.Append(LastName);
        sb.Append(",UserId: ");
        sb.Append(UserId);
        sb.Append(",TownId: ");
        sb.Append(TownId);
        sb.Append(",CityId: ");
        sb.Append(CityId);
        sb.Append(",Gender: ");
        sb.Append(Gender);
        sb.Append(",GenderAsText: ");
        sb.Append(GenderAsText);
        sb.Append(")");
        return sb.ToString();
    }
}
