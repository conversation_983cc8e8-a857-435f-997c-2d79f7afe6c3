namespace Bigpara.Domain.Bigpara;

public partial class AuthorAnswers
{
    public int Id { get; set; }
    public int QuestionId { get; set; }
    public string QuestionTitle { get; set; }
    public string Question { get; set; }
    public DateTime CreatedDate { get; set; }
    public int CategoryId { get; set; }
    public string CategoryName { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Answer { get; set; }
    public DateTime AnswerDate { get; set; }
    public int AuthorId { get; set; }
    public string Followers { get; set; }

    public string UserFullName
    {
        get
        {
            return FirstName + " " + LastName;
        }
    }
}
