﻿namespace Bigpara.Core.Extensions;

public static class ConvertExtension
{
    public static int ToInt32(this object obj)
    {
        if (obj == null) return 0;
        int converted = 0;
        bool result = int.TryParse(obj.ToString(), out converted);
        return converted;
    }

    public static byte ToByte(this object obj)
    {
        if (obj == null) return 0;
        byte converted = 0;
        bool result = byte.TryParse(obj.ToString(), out converted);
        return converted;
    }

    public static DateTime ToDatetime(this object obj)
    {
        if (obj == null) return DateTime.MinValue;
        DateTime converted = DateTime.MinValue;
        bool result = DateTime.TryParse(obj.ToString(), out converted);
        return converted;
    }

    public static double ToDouble(this object obj)
    {
        if (obj == null) return 0;
        double converted = 0;
        bool result = double.TryParse(obj.ToString(), out converted);
        return converted;
    }

    public static double ToFloat(this object obj)
    {
        if (obj == null) return 0;
        float converted = 0;
        bool result = float.TryParse(obj.ToString(), out converted);
        return converted;
    }
}
