﻿using Bigpara.Cache.Interfaces;
using Microsoft.Extensions.Caching.Memory;

namespace Bigpara.Cache.Memory.Services;

public class MemoryCacheService : ICacheService
{
    public int CacheDuration => 15; // default duration in minutes

    private readonly IMemoryCache _cache;

    public MemoryCacheService(IMemoryCache cache)
    {
        _cache = cache;
    }

    public T Get<T>(string key, int cacheTime, Func<T> acquire)
    {
        if (_cache.TryGetValue(key, out T result))
            return result;

        result = acquire();

        if (result is not null && cacheTime > 0)
            Set(key, result, cacheTime);

        return result;
    }

    public async Task<T> GetAsync<T>(string key, int cacheTime, Func<Task<T>> acquire)
    {
        if (_cache.TryGetValue(key, out T result))
            return result;

        result = await acquire();

        if (result is not null && cacheTime > 0)
            Set(key, result, cacheTime);

        return result;
    }

    public T Get<T>(string key)
    {
        _cache.TryGetValue(key, out T result);
        return result;
    }

    public T Get<T>(string key, out bool isSucceeded)
    {
        isSucceeded = _cache.TryGetValue(key, out T result);
        return result;
    }

    public IDictionary<string, T> Get<T>(IList<string> keys)
    {
        var result = new Dictionary<string, T>();

        foreach (var key in keys)
        {
            if (_cache.TryGetValue(key, out T value))
                result[key] = value;
        }

        return result;
    }

    public void Set<T>(string key, T value)
    {
        Set(key, value, CacheDuration);
    }

    public void Set<T>(string key, T value, int cacheTime)
    {
        if (value is not null)
            _cache.Set(key, value, TimeSpan.FromMinutes(cacheTime));
    }

    public void Set<T>(string key, T value, TimeSpan timeout)
    {
        if (value is not null)
            _cache.Set(key, value, timeout);
    }

    public bool IsSet(string key)
    {
        return _cache.TryGetValue(key, out _);
    }

    public void Remove(string key)
    {
        _cache.Remove(key);
    }

    public void RemoveByPattern(string pattern)
    {
        // MemoryCache does not support pattern-based eviction natively.
        // You need to implement key tracking externally if needed.
        throw new NotSupportedException("RemoveByPattern is not supported in MemoryCache.");
    }

    public void Clear()
    {
        // MemoryCache does not support full clear directly.
        // Needs custom implementation via key tracking if desired.
        throw new NotSupportedException("Clear is not supported in MemoryCache without key tracking.");
    }

    public void Update<T>(string listKey, T value)
    {
        if (IsSet(listKey))
            Set(listKey, value);
    }

    public void Clear(string key)
    {
        Remove(key);
    }

    public void Add<T>(string key, T value) where T : class
    {
        Set(key, value);
    }

    public void Add<T>(string key, T value, TimeSpan timeout) where T : class
    {
        Set(key, value, timeout);
    }

    public void Add<T>(string key, T value, int cacheTime) where T : class
    {
        Set(key, value, cacheTime);
    }
}
