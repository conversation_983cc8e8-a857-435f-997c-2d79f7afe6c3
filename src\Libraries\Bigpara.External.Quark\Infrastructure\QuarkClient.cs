﻿using DnsClient.Internal;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Quark.Core.Helpers;
using System;
using System.Collections.Generic;
using System.Data.Services.Client;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.External.Quark.Infrastructure
{
    public class QuarkClient : IQuarkClient
    {

        private readonly HttpClient _httpClient;
        private readonly ILogger<QuarkClient> _logger;
        private const string ServerDateCacheKey = "ServerDate";
        private const int ServerDateCacheSecond = 30;

        private readonly string DataServiceUrl;
        private readonly string AppId;
        private readonly string AppSecret;
        private IMemoryCache? _memoryCache;
        public QuarkClient(IHttpClientFactory httpClientFactory, IMemoryCache memoryCache, IConfiguration configuration, ILogger<QuarkClient> logger)
        {
            _memoryCache = memoryCache;
            _httpClient = httpClientFactory.CreateClient("quark");
            _logger = logger;
            DataServiceUrl = configuration?.GetValue<string>("Quark:DataService:Url")
                ?? throw new ArgumentNullException(nameof(configuration), "Configuration or Quark:DataService:Url is null.");
            AppId = configuration?.GetValue<string>("Quark:AppId")
                ?? throw new ArgumentNullException(nameof(configuration), "Configuration or Quark:AppId is null.");
            AppSecret = configuration?.GetValue<string>("Quark:AppSecret")
                ?? throw new ArgumentNullException(nameof(configuration), "Configuration or Quark:AppSecret is null.");
            
        }
        private DateTime GetServerDateFromApi(string dataServiceUrl = null)
        {

            dataServiceUrl = string.IsNullOrEmpty(dataServiceUrl) ? DataServiceUrl : dataServiceUrl;

            var response = _httpClient.GetStringAsync($"{dataServiceUrl}/date?format=json").Result;

            var serverDate = JsonConvert.DeserializeObject<DateTime>(response);
            return serverDate;

        }

        private DateTime GetServerDate(string dataServiceUrl = null)
        {

            if (!_memoryCache.TryGetValue(ServerDateCacheKey, out DateTime serverDate))
            {
                serverDate = GetServerDateFromApi(dataServiceUrl);

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetAbsoluteExpiration(TimeSpan.FromSeconds(ServerDateCacheSecond));

                _memoryCache.Set(ServerDateCacheKey, serverDate, cacheEntryOptions);
            }

            return serverDate;
        }

        private void SetupHttpClient(string dataServiceUrl = null)
        {
            var serverDate = GetServerDate(dataServiceUrl);
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("X-AppId", AppId);
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Date", serverDate.ToUniversalTime().ToString("r"));
            _httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization",
                AppId + ":" +
                HMACSHA1Helper.Encode(AppSecret, serverDate.ToUniversalTime().ToString("r")));
        }


        /// <summary>
        /// Linq ile Odata query leri oluşturmak için
        /// yazılan linq sorgusu çalıştırılmadan önce odata cümlesine döner ve api ye GetAsync methodları ile gönderilir
        /// Methodlar async olarak çalışır.
        /// </summary>
        /// <typeparam name="T">Query Generic Tipi</typeparam>
        /// <param name="entitySetName">/api/{entitysetname} kısmındaki setname <example>/api/contents</example> </param>
        /// <returns>IQueryable'dan kalıtım almış olan DataServiceQuery</returns>
        public DataServiceQuery<T> CreateQuery<T>(string entitySetName) where T : class
        {
            var context = new DataServiceContext(new Uri(DataServiceUrl), System.Data.Services.Common.DataServiceProtocolVersion.V3);

            return context.CreateQuery<T>(entitySetName);
        }

        /// <summary>
        /// Gönderilen query yi async olarak api ye istek gönderen method
        /// </summary>
        /// <typeparam name="T">Generic Type</typeparam>
        /// <param name="query">Linq Querysi</param>
        /// <param name="cancelToken">Async methodu iptal etme token ı </param>
        /// <returns>Deserialize edilmiş Task sonucu</returns>
        public async Task<T> GetAsync<T>(IQueryable query,
            CancellationToken cancelToken = default(CancellationToken),
            string extraQuery = null)
        {
            var currentQuery = query as DataServiceQuery;

            var originalUri = currentQuery?.RequestUri;
            if (originalUri == null)
            {
                throw new ArgumentNullException(nameof(query), "Query must be of type DataServiceQuery.");
            }
            var uriString = originalUri.ToString();
            if (!string.IsNullOrWhiteSpace(extraQuery))
            {
                if (uriString.Contains("?"))
                {
                    uriString += "&" + extraQuery;
                }
                else
                {
                    uriString += "?" + extraQuery;
                }
            }
            return await GetAsync<T>(uri: new Uri(uriString), cancelToken: cancelToken);

        }

        /// <summary>
        /// Gönderilen query yi sync olarak api ye istek gönderen method
        /// </summary>
        /// <typeparam name="T">Generic Type</typeparam>
        /// <param name="query">Linq Querysi</param>
        /// <returns>Deserialize edilmiş Task sonucu</returns>
        public T Get<T>(IQueryable query, string extraQuery = null)
        {
            var currentQuery = query as DataServiceQuery;

            var originalUri = currentQuery?.RequestUri;

            if (originalUri == null)
            {
                throw new ArgumentNullException(nameof(query), "Query must be of type DataServiceQuery.");
            }

            var uriString = originalUri.ToString();

            if (!string.IsNullOrWhiteSpace(extraQuery))
            {
                if (uriString.Contains("?"))
                {
                    uriString += "&" + extraQuery;
                }
                else
                {
                    uriString += "?" + extraQuery;
                }
            }
            return Get<T>(uri: new Uri(uriString));
        }

        /// <summary>
        /// Gönderilen url i async olarak api ye istek gönderen method
        /// </summary>
        /// <typeparam name="T">Generic Type</typeparam>
        /// <param name="uri">Uri</param>
        /// <param name="cancelToken">Async methodu iptal etme token ı </param>
        /// <returns>Deserialize edilmiş Task sonucu</returns>
        public async Task<T> GetAsync<T>(Uri uri, CancellationToken cancelToken = default(CancellationToken))
        {
            SetupHttpClient();

            string url;
            if (uri.IsAbsoluteUri)
            {
                url = uri.OriginalString;
            }
            else
            {
                url = DataServiceUrl + uri.ToString();
            }
            HttpResponseMessage response;
            try
            {
                response = await _httpClient.GetAsync(url);
            }
            catch (Exception)
            {
                response = null;
            }
            if (response == null)
            {
                _logger.LogError("Response is null for URL: {Url}", url);
                return await Task.FromResult<T>(default(T));
            }

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Request failed with status code {StatusCode} for URL: {Url}", response.StatusCode, url);
                return await Task.FromResult<T>(default(T));
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var settings = new JsonSerializerSettings();
            settings.NullValueHandling = NullValueHandling.Ignore;
            settings.MissingMemberHandling = MissingMemberHandling.Ignore;
            return await Task.Factory.StartNew(() => JsonConvert.DeserializeObject<T>(responseContent, settings));

        }

        /// <summary>
        /// Gönderilen url i sync olarak api ye istek gönderen method
        /// </summary>
        /// <typeparam name="T">Generic Type</typeparam>
        /// <param name="uri">Uri</param>
        /// <returns>Deserialize edilmiş Task sonucu</returns>
        public T Get<T>(Uri uri)
        {

            SetupHttpClient();

            string url;
            if (uri.IsAbsoluteUri)
            {
                url = uri.OriginalString;
            }
            else
            {
                url = DataServiceUrl + uri.ToString();
            }


            using (HttpResponseMessage response = _httpClient.GetAsync(url).Result)
            {

                if (!response.IsSuccessStatusCode)
                {
                    throw new Exception(response.Content.ReadAsStringAsync().Result);
                }

                var settings = new JsonSerializerSettings();
                settings.NullValueHandling = NullValueHandling.Ignore;
                settings.MissingMemberHandling = MissingMemberHandling.Ignore;
                return JsonConvert.DeserializeObject<T>(response.Content.ReadAsStringAsync().Result, settings);

            }

        }

        /// <summary>
        /// Async olarak Post methodu
        /// verilen url e async olarak post işlemi gerçekleştirir.
        /// </summary>
        /// <typeparam name="T">Json olarak gönderilecek tip</typeparam>
        /// <param name="uri">Uri</param>
        /// <param name="value">Json olarak gönderilecek değer</param>
        /// <param name="cancelToken">İptal token ı</param>
        /// <returns></returns>
        public async Task<T> PostAsync<T>(Uri uri, T value, CancellationToken cancelToken = default(CancellationToken))
        {
            return await PostAsync<T, T>(uri, value, cancelToken);
        }

        public async Task<TOut> PostAsync<T, TOut>(Uri uri, T value, CancellationToken cancelToken = default(CancellationToken), string dataServiceUrl = null)
        {
            SetupHttpClient(dataServiceUrl: dataServiceUrl);


            string url;
            if (uri.IsAbsoluteUri)
            {
                url = uri.OriginalString;
            }
            else
            {
                url = DataServiceUrl + uri.ToString();
            }

            var response = await _httpClient.PostAsJsonAsync<T>(url, value);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception(response.Content.ReadAsStringAsync().Result);
            }

            return await response.Content.ReadFromJsonAsync<TOut>();
        }


        public async Task<T> PostAsStringAsync<T>(Uri uri, T value, CancellationToken cancelToken = default(CancellationToken))
        {
            SetupHttpClient();


            string url;
            if (uri.IsAbsoluteUri)
            {
                url = uri.OriginalString;
            }
            else
            {
                url = DataServiceUrl + uri.ToString();
            }

            var response = await _httpClient.PostAsJsonAsync<T>(url, value);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception(response.Content.ReadAsStringAsync().Result);
            }

            var resultAsString = await response.Content.ReadAsStringAsync();
            return JsonConvert.DeserializeObject<T>(resultAsString);

        }

        public async Task<TOut> PostDataAsync<TOut>(Uri uri, MultipartFormDataContent values, CancellationToken cancelToken = default(CancellationToken))
        {
            SetupHttpClient();

            string url;
            if (uri.IsAbsoluteUri)
            {
                url = uri.OriginalString;
            }
            else
            {
                url = DataServiceUrl + uri.ToString();
            }

            var content = values;
            var response = await _httpClient.PostAsync(url, content);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception(response.Content.ReadAsStringAsync().Result);
            }

            return await response.Content.ReadFromJsonAsync<TOut>();

        }
        public async Task<TOut> PostDataAsync<TOut>(Uri uri, Stream value, string fileName, string contentType, CancellationToken cancelToken = default(CancellationToken))
        {

            SetupHttpClient();
            string url;
            if (uri.IsAbsoluteUri)
            {
                url = uri.OriginalString;
            }
            else
            {
                url = DataServiceUrl + uri.ToString();
            }

            var content = new MultipartFormDataContent();

            var streamContent = new StreamContent(value);
            streamContent.Headers.ContentType = new MediaTypeHeaderValue(contentType);
            content.Add(streamContent, "file", fileName);


            var response = await _httpClient.PostAsync(url, content);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception(response.Content.ReadAsStringAsync().Result);
            }

            return await response.Content.ReadFromJsonAsync<TOut>();

        }


        /// <summary>
        /// Async olarak Put methodu
        /// verilen url e async olarak put işlemi gerçekleştirir.
        /// </summary>
        /// <typeparam name="T">Json olarak gönderilecek tip</typeparam>
        /// <param name="uri">Uri</param>
        /// <param name="value">Json olarak gönderilecek değer</param>
        /// <param name="cancelToken">İptal token ı</param>
        /// <returns></returns>
        public async Task<T> PutAsync<T>(Uri uri, T value, CancellationToken cancelToken = default(CancellationToken)) where T : class
        {

            SetupHttpClient();
            string url;
            if (uri.IsAbsoluteUri)
            {
                url = uri.OriginalString;
            }
            else
            {
                url = DataServiceUrl + uri.ToString();
            }
            var response = await _httpClient.PutAsJsonAsync<T>(url, value);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception(response.Content.ReadAsStringAsync().Result);
            }
            return await response.Content.ReadFromJsonAsync<T>();

        }

        /// <summary>
        /// Async olarak Delete methodu
        /// verilen url e async olarak delete işlemi gerçekleştirir.
        /// </summary>
        /// <typeparam name="T">Json olarak gönderilecek tip</typeparam>
        /// <param name="uri">Uri</param>
        /// <param name="value">Json olarak gönderilecek değer</param>
        /// <param name="cancelToken">İptal token ı</param>
        /// <returns></returns>
        public async Task DeleteAsync(Uri uri,
            CancellationToken cancelToken = default(CancellationToken))
        {

            SetupHttpClient();
            string url;
            if (uri.IsAbsoluteUri)
            {
                url = uri.OriginalString;
            }
            else
            {
                url = DataServiceUrl + uri.ToString();
            }
            var response = await _httpClient.DeleteAsync(url, cancelToken);
            if (!response.IsSuccessStatusCode)
            {
                throw new Exception(response.Content.ReadAsStringAsync().Result);
            }

        }

        //WithWait ile biten methodlar dısarıdan data alınması gerekiyor ve bu methodlar ayrı bir projenin içerisinden cagrilmasi gerekiyor ise kullanılmalıdır. 
        //Console app lerde withwait olmayanlar kullanılabilir ancak web applerde async tasklar problem yaratır. !!!! 
        #region ApiMethodsWithWait-Dis Projeler
        public Task<TOut> PostDataAsyncWithWait<TOut>(Uri uri, Stream value, string fileName, string contentType, CancellationToken cancelToken = default(CancellationToken))
        {

            SetupHttpClient();
            string url = uri.IsAbsoluteUri
                ? uri.OriginalString
                : DataServiceUrl + uri.ToString();

            var content = new MultipartFormDataContent();

            var streamContent = new StreamContent(value);
            streamContent.Headers.ContentType = new MediaTypeHeaderValue(contentType);
            content.Add(streamContent, "file", fileName);


            var response = _httpClient.PostAsync(url, content, cancelToken);
            Task.WaitAll(response);


            if (!response.Result.IsSuccessStatusCode)
            {
                throw new Exception(response.Result.Content.ReadAsStringAsync().Result);
            }

            return response.Result.Content.ReadFromJsonAsync<TOut>(cancelToken);

        }

        public Task<TOut> PostAsyncWithWait<T, TOut>(Uri uri, T value)
        {
            var postTask = PostAsyncWithWait<T, TOut>(uri, value, default(CancellationToken));
            Task.WaitAll(postTask);
            return postTask;

        }

        public Task<TOut> PostAsyncWithWait<T, TOut>(Uri uri, T value, CancellationToken cancelToken = default(CancellationToken))
        {

            SetupHttpClient();
            string url = uri.IsAbsoluteUri
                ? uri.OriginalString
                : DataServiceUrl + uri.ToString();

            var response = _httpClient.PostAsJsonAsync<T>(url, value, cancelToken);
            Task.WaitAll(response);

            if (!response.Result.IsSuccessStatusCode)
            {
                throw new Exception(response.Result.Content.ReadAsStringAsync().Result);
            }

            return response.Result.Content.ReadFromJsonAsync<TOut>(cancelToken);

        }
        public Task<T> PutAsyncWithWait<T>(Uri uri, T value, CancellationToken cancelToken = default(CancellationToken)) where T : class
        {

            SetupHttpClient();
            string url = uri.IsAbsoluteUri
                ? uri.OriginalString
                : DataServiceUrl + uri.ToString();
            var response = _httpClient.PutAsJsonAsync<T>(url, value);
            Task.WaitAll(response);
            if (!response.Result.IsSuccessStatusCode)
            {
                throw new Exception(response.Result.Content.ReadAsStringAsync().Result);
            }
            return response.Result.Content.ReadFromJsonAsync<T>();

        }
        public Task DeleteAsyncWithWait(Uri uri, CancellationToken cancelToken = default(CancellationToken))
        {

            SetupHttpClient();
            string url = uri.IsAbsoluteUri
                ? uri.OriginalString
                : DataServiceUrl + uri.ToString();
            var response = _httpClient.DeleteAsync(url, cancelToken);
            Task.WaitAll(response);
            if (!response.Result.IsSuccessStatusCode)
            {
                throw new Exception(response.Result.Content.ReadAsStringAsync().Result);
            }
            return response;

        }
        #endregion

    }
}
