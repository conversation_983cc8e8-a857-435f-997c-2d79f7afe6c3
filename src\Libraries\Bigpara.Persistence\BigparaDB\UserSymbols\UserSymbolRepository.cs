﻿using Bigpara.Core.Application.Contracts.BigparaDB.UserSymbols;
using Bigpara.Domain.Bigpara;
using Microsoft.Data.SqlClient;
using Quark.Models.Entities.Users;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Bigpara.Persistence.BigparaDB.UserSymbols
{
    public class UserSymbolRepository : IUserSymbolRepository
    {
        private readonly IBigparaDbContext _bigparaDbContext;
        public UserSymbolRepository(IBigparaDbContext bigparaDbContext)
        {
            _bigparaDbContext = bigparaDbContext;
        }
        public async Task AddOrDeleteAsync(int operation,UserSembol userSembol)
        {
            var parameters = new List<SqlParameter>
            {
            new SqlParameter("userId", userSembol.UserId),
            new SqlParameter("pageType",userSembol.PageType),
            new SqlParameter("sembolId", userSembol.SembolId),
            new SqlParameter("opId", operation)
            };
         
            await _bigparaDbContext.ExecuteNonQueryAsync("spCreateOrUpdateUserSembol", parameters.ToArray());
        }

        public Task<UserSembol> GetUserSembolAsync(int userId, int sembolId)
        {
            throw new NotImplementedException();
        }

        public async Task<List<UserSembol>> ListAsync(int userId, int pageIndex, int paseSize)
        {
            var data= await _bigparaDbContext.ExecuteStoredProcedureAsync<UserSembol>("spGetUserHisseList", new SqlParameter[]
            {
                new SqlParameter("userId", userId)
            });

            return data;
        }


    }
}
