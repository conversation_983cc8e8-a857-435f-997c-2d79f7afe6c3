﻿using Bigpara.Core.Application.Contracts.Matriks.Grafiks;
using Bigpara.Domain;
using Bigpara.Domain.Matriks;
using Bigpara.Persistence.Matriks.Grafiks;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services
{
    public class GrafikService : IGrafikService
    {
        private readonly IGrafikRepository _grafikGunlukRepository;
        public GrafikService(IGrafikRepository grafikGunlukRepository)
        {
            _grafikGunlukRepository = grafikGunlukRepository;
        }


        public async Task<List<GrafikGunluk>> GetTopGrafikGunluks(int sembolId,int top)
        {
            return await  _grafikGunlukRepository.GetGrafikGunluksBySembolId(sembolId,top);
        }


        public async Task<List<Grafik5Dk>> GetGrafik5DkBySembolIds(string Ids,int perIdRecordCount)
        {
          return await  _grafikGunlukRepository.GetGrafik5DkBySembolIds(Ids, perIdRecordCount);
        }

        public async Task<List<GrafikGunluk>> GetGrafikGunluks(int sembolId, int top)
        {
            throw new NotImplementedException();
        }
    }
}
