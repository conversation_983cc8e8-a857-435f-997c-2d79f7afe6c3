﻿using Bigpara.Core.Application.Common;
using Bigpara.Domain;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Bigpara.Persistence;

public class GenericRepository<T,TContext> : IGenericRepository<T,TContext> where T : BaseEntity where TContext : DbContext
{
    private readonly TContext _context;
    public GenericRepository(TContext context = null)
    {
        _context = context;
    }
    public T Add(T entity)
    {
        _context.Set<T>().Add(entity);
        _context.SaveChanges();
        return entity;
    }

    public async Task<T> AddAsync(T entity)
    {
        _context.Set<T>().Add(entity);
        await _context.SaveChangesAsync();
        return entity;
    }

    public int Count()
    {
        return _context.Set<T>().Count();
    }

    public async Task<int> CountAsync()
    {
        return await _context.Set<T>().CountAsync();
    }

    public void Delete(T t)
    {
        _context.Set<T>().Remove(t);
        _context.SaveChanges();
    }

    public async Task<int> DeleteAsync(T t)
    {
        _context.Set<T>().Remove(t);
        return await _context.SaveChangesAsync();
    }


    public bool Exist(Expression<Func<T, bool>> predicate)
    {
        var exist = _context.Set<T>().Where(predicate);
        return exist.Any() ? true : false;
    }

    public IEnumerable<T> Filter(Expression<Func<T, bool>> filter = null, Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null, string includeProperties = "", int? page = null, int? pageSize = null)
    {
        IQueryable<T> query = _context.Set<T>();
        if (filter != null)
        {
            query = query.Where(filter);
        }

        if (orderBy != null)
        {
            query = orderBy(query);
        }

        if (includeProperties != null)
        {
            foreach (
                var includeProperty in includeProperties.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
            {
                query = query.Include(includeProperty);
            }
        }

        if (page != null && pageSize != null)
        {
            query = query.Skip((page.Value - 1) * pageSize.Value).Take(pageSize.Value);
        }

        return query.ToList();
    }

    public T Find(Expression<Func<T, bool>> match)
    {
        return _context.Set<T>().SingleOrDefault(match);
    }

    public ICollection<T> FindAll(Expression<Func<T, bool>> match)
    {
        return _context.Set<T>().Where(match).ToList();
    }

    public async Task<ICollection<T>> FindAllAsync(Expression<Func<T, bool>> match)
    {
        return await _context.Set<T>().Where(match).ToListAsync();
    }

    public async Task<T> FindAsync(Expression<Func<T, bool>> match)
    {
        return await _context.Set<T>().SingleOrDefaultAsync(match);
    }

    public IQueryable<T> FindBy(Expression<Func<T, bool>> predicate)
    {
        return _context.Set<T>().Where(predicate);
    }

    public IQueryable<T> FindAll()
    {
        return _context.Set<T>();
    }

    public async Task<T> LastOrDefault(Expression<Func<T, bool>> predicate)
    {
        return await _context.Set<T>().Where(predicate).LastOrDefaultAsync();
    }

    public ICollection<T> GetAll()
    {

        try
        {
            return _context.Set<T>().ToList();
        }
        catch (Exception ex)
        {

            Console.WriteLine(ex.Message);
            return null;
        }

    }

    public async Task<ICollection<T>> GetAllAsync()
    {
        return await _context.Set<T>().ToListAsync();
    }

    public T GetById(int id)
    {
        return _context.Set<T>().Find(id);
    }

    public async Task<T> GetByIdAsync(int id)
    {
        return await _context.Set<T>().FindAsync(id);
    }

    public T GetByUniqueId(string id)
    {
        return _context.Set<T>().Find(id);

    }

    public async Task<T> GetByUniqueIdAsync(string id)
    {
        return await _context.Set<T>().FindAsync(id);
    }

    public IQueryable<T> Query(Expression<Func<T, bool>> filter = null, Func<IQueryable<T>, IOrderedQueryable<T>> orderBy = null)
    {
        IQueryable<T> query = _context.Set<T>();

        if (filter != null)
            query = query.Where(filter);

        if (orderBy != null)
            query = orderBy(query);

        return query;
    }

    public T Update(T updated)
    {
        if (updated == null)
        {
            return null;
        }

        _context.Set<T>().Attach(updated);
        _context.Entry(updated).State = EntityState.Modified;
        _context.SaveChanges();

        return updated;
    }

    public async Task<T> UpdateAsync(T updated)
    {
        if (updated == null)
        {
            return null;
        }

        _context.Set<T>().Attach(updated);
        _context.Entry(updated).State = EntityState.Modified;
        await _context.SaveChangesAsync();

        return updated;
    }
}
