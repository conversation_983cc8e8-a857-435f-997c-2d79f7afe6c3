﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Bigpara.Service.Hangfire.Helpers;
using Bigpara.Service.Hangfire.Infrastructre.Data;
using Bigpara.Service.Hangfire.Services.Interfaces;
using Bigpara.Service.Hangfire.Infrastructre.HttpClients.Interfaces;
using Bigpara.Service.Hangfire.Infrastructre.Publishers;

namespace Bigpara.Service.Hangfire.Services;

public class HisseService : IHisseService
{
    private readonly IStoredProcedureParameterService _storedProcedureService;
    private readonly ISymbolService _symbolService;
    private readonly IPublisher _publisher;

    private readonly TimeZoneInfo _turkeyTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");

    public HisseService
    (
        IStoredProcedureParameterService storedProcedureService,
        ISymbolService symbolService,
        IPublisher publisher)
    {
        _storedProcedureService = storedProcedureService;
        _symbolService = symbolService;
        _publisher = publisher;
    }

    public async Task Change(YuzeyselDto item)
    {
        if (item == null)
            return;

        var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

        var symbols = await _symbolService.GetSymbolByCodeAsync(item.SEMBOL);

        if (symbols == null)
        {
            return;
        }

        //symbols.LastChangeTime = item.UnixTime;
        item.Security = symbols.Sembol.ImkbHisseTipString;
        item.XU100AG = YuzeyselHelper.GetXU100AGFromJson(symbols.Sembol.IndexWeight);

        if (!string.IsNullOrWhiteSpace(item.Security) && !string.IsNullOrWhiteSpace(item.SEMBOL))
        {
            if (item.Security.Equals("V") && item.SEMBOL.EndsWith("V"))
            {
                item.SEMBOL = item.SEMBOL.Substring(0, item.SEMBOL.Length - 1);
            }
        }

        item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
            DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
            _turkeyTimeZone);

        var hisseAcilisParameters = storedProcedureHelper.CheckHisseAcilisParameter(item);
        if (hisseAcilisParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseAcilis", hisseAcilisParameters, item.SEMBOL);

        var hisseHistoricalParameters = storedProcedureHelper.CheckHisseTarihselParameter(item);
        if (hisseHistoricalParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseTarihsel", hisseHistoricalParameters, item.SEMBOL);

        var hisseİstatistikParameters = storedProcedureHelper.CheckHisseIstatistikParameter(item);
        if (hisseİstatistikParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseIstatistik", hisseİstatistikParameters, item.SEMBOL);

        var hisseKapanisParameters = storedProcedureHelper.CheckHisseKapanisParameter(item);
        if (hisseKapanisParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseKapanis", hisseKapanisParameters, item.SEMBOL);

        var hisseMarjParameters = storedProcedureHelper.CheckHisseMarjParameter(item, symbols);
        if (hisseMarjParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseMarj", hisseMarjParameters, item.SEMBOL);

        var hisseOrtalamaParameters = storedProcedureHelper.CheckHisseOrtalamaParameter(item);
        if (hisseOrtalamaParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseOrtalama", hisseOrtalamaParameters, item.SEMBOL);

        var hisseSonParameters = storedProcedureHelper.CheckHisseSonParameter(item);
        if (hisseSonParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseSon", hisseSonParameters, item.SEMBOL);

        var hisseToplamParameters = storedProcedureHelper.CheckHisseToplamParameter(item);
        if (hisseToplamParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseToplam", hisseToplamParameters, item.SEMBOL);
    }

    public async Task ChangeRealTime(YuzeyselDto item)
    {
        if (item == null)
            return;

        var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

        var symbols = await _symbolService.GetSymbolByCodeAsync(item.SEMBOL);

        if (symbols == null)
        {
            return;
        }

        // symbols.LastChangeTime = item.UnixTime;
        item.Security = symbols.Sembol.ImkbHisseTipString;

        if (!string.IsNullOrWhiteSpace(item?.Security) && !string.IsNullOrWhiteSpace(item?.SEMBOL))
        {
            if (item.Security.Equals("V") && item.SEMBOL.EndsWith("V"))
            {
                item.SEMBOL = item.SEMBOL.Substring(0, item.SEMBOL.Length - 1);
            }
        }

        item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
            DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
            _turkeyTimeZone);

        var hisseİstatistikRealTimeParameters = storedProcedureHelper.CheckHisseIstatistikRealTimeParameter(item);
        if (hisseİstatistikRealTimeParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseIstatistik_Realtime", hisseİstatistikRealTimeParameters, item.SEMBOL);

        var hisseOrtalamaRealTimeParameters = storedProcedureHelper.CheckHisseOrtalamaRealTimeParameter(item);
        if (hisseOrtalamaRealTimeParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseOrtalama_RealTime", hisseOrtalamaRealTimeParameters, item.SEMBOL);

        var hisseSonRealTimeParameters = storedProcedureHelper.CheckHisseSonRealTimeParameter(item);
        if (hisseSonRealTimeParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseSon_Realtime", hisseSonRealTimeParameters, item.SEMBOL);
    
    }

    public async Task ChangeHistorical(YuzeyselDto item)
    {
        if (item == null)
            return;

        var storedProcedureHelper = new StoredProcedureHelper(_storedProcedureService);

        var symbols = await _symbolService.GetSymbolByCodeAsync(item.SEMBOL);

        if (symbols == null)
        {
            return;
        }

        //symbols.LastChangeTime = item.UnixTime;
        item.Security = symbols.Sembol.ImkbHisseTipString;

        if (!string.IsNullOrWhiteSpace(item.Security) && !string.IsNullOrWhiteSpace(item.SEMBOL))
        {
            if (item.Security.Equals("V") && item.SEMBOL.EndsWith("V"))
            {
                item.SEMBOL = item.SEMBOL.Substring(0, item.SEMBOL.Length - 1);
            }
        }

        item.Tarih = TimeZoneInfo.ConvertTimeFromUtc(
            DateTimeOffset.FromUnixTimeMilliseconds(item.UnixTime).UtcDateTime,
            _turkeyTimeZone);

        var hisseHistoricalParameters = storedProcedureHelper.CheckHisseTarihselParameter(item);
        if (hisseHistoricalParameters != null)
            await _storedProcedureService.ExecuteStoredProcedureAsync( "sp_foreks_ImkbHisseTarihsel", hisseHistoricalParameters, item.SEMBOL);
    }

    public async Task SendRealTimeApi(Dictionary<string, string> values)
    {
        await _publisher.PublishSymbolDetailAsync(values["SEMBOL"], values);
    }
}
