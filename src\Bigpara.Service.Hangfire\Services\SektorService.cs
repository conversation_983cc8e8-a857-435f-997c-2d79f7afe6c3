﻿using Bigpara.Cache.Interfaces;
using Bigpara.Core.Application.Contracts.Matriks.Sektor;
using Bigpara.Domain;
using Bigpara.Service.Hangfire.Services.Interfaces;

namespace Bigpara.Service.Hangfire.Services;

public class SektorService : ISektorService
{
    private const string CachekeyAllSektor = "bigpara.all.sektorler";


    private readonly ISektorRepository _sektorRepository;
    private readonly ICacheService _cacheService;

    public SektorService(ISektorRepository sektorRepository, ICacheService cacheService)
    {
        _sektorRepository = sektorRepository;
        _cacheService = cacheService;
    }

    public virtual async Task<List<Sektorler>> GetSektorler()
    {
        return await _cacheService.GetAsync(CachekeyAllSektor, 15, _sektorRepository.GetSektorList);
    }

    public async Task<Sektorler> GetSektorById(string sectorCode)
    {
        return (await GetSektorler()).FirstOrDefault(c => c.StrKod.Equals(sectorCode));
    }
}
