﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Bigpara.Persistence.PostgreSql.Extensions;

public static class ServiceCollectionExtensions
{
    //public static IServiceCollection AddPostgreSQL(this IServiceCollection services, IConfiguration Configuration)
    //{
    //    var appCfg = Configuration[$"Global:PostgreSql"];
    //    services.AddDbContext<DataBaseContext>(options =>
    //    {
    //        options.UseNpgsql(appCfg);
    //    });

    //   // services.AddScoped(typeof(IGenericRepository<>), typeof(GenericRepository<>));

    //    return services;
    //}
}
