﻿using Bigpara.Service.Hangfire.Infrastructre.Dtos.Foreks;
using Bigpara.Domain;

namespace Bigpara.Service.Hangfire.Services.Interfaces;
public interface ISymbolService
{
    Task<SymbolsCacheDto?> GetSymbolByCodeAsync(string symbolCode);
    Task<SymbolsCacheDto?> GetSymbolByIdAsync(string _id);
    Task<SymbolsCacheDto?> GetSymbolByLegacyCodeAsync(string legacyCode);
    Task SetSymbolAsync(string cacheKey, ForeksSembolDto symbol);
    Task Change(ForeksSembolDto symbol, int feedItemId, List<Sektorler> allSektorler);
    Task ChangePassive(string strKod);
}
