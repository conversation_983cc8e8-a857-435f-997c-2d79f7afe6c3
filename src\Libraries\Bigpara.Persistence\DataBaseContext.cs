﻿using Bigpara.Domain;
using Bigpara.Domain.Matriks;
using Bigpara.Persistence.Mapping;
using Microsoft.EntityFrameworkCore;
using System.Reflection;

namespace Bigpara.Persistence;





//public partial class DataBaseContext : DbContext
//{
//    public DataBaseContext(DbContextOptions<DataBaseContext> options) : base(options)
//    {
//    }



//    protected override void OnModelCreating(ModelBuilder modelBuilder)
//    {
//        RegisterEntityMapping(modelBuilder);
//        base.OnModelCreating(modelBuilder);
//    }

//    public void RegisterEntityMapping(ModelBuilder modelBuilder)
//    {
//        var typeConfigurations = Assembly.GetEntryAssembly().GetTypes().Where(type =>
//                (type.BaseType?.IsGenericType ?? false)
//                    && (type.BaseType.GetGenericTypeDefinition() == typeof(MappinEntityTypeConfiguration<>)
//                        || type.BaseType.GetGenericTypeDefinition() == typeof(MappinEntityTypeConfiguration<>)));

//        foreach (var typeConfiguration in typeConfigurations)
//        {
//            var configuration = (IMappingConfiguration)Activator.CreateInstance(typeConfiguration);
//            configuration.ApplyConfiguration(modelBuilder);
//        }

//    }

//    public virtual new DbSet<TEntity> Set<TEntity>() where TEntity : class
//    {
//        return base.Set<TEntity>();
//    }

//    //protected virtual string CreateSqlWithParameters(string sql, params object[] parameters)
//    //{
//    //    //add parameters to sql

//    //    for (var i = 0; i <= (parameters?.Length ?? 0) - 1; i++)
//    //    {
//    //        if (!(parameters[i] is DbParameter parameter))
//    //            continue;

//    //        sql = $"{sql}{(i > 0 ? "," : string.Empty)} @{parameter.ParameterName}";

//    //        //whether parameter is output
//    //        if (parameter.Direction == ParameterDirection.InputOutput || parameter.Direction == ParameterDirection.Output)
//    //            sql = $"{sql} output";

//    //    }

//    //    return sql;

//    //}


//    //public virtual IQueryable<TQuery> QueryFromSql<TQuery>(string sql, params object[] parameters) where TQuery : class
//    //{
//    //    return Query<TQuery>().FromSqlRaw(CreateSqlWithParameters(sql, parameters), parameters);
//    //}
//    //public virtual IQueryable<TEntity> EntityFromSql<TEntity>(string sql, params object[] parameters) where TEntity : class
//    //{
//    //    return Set<TEntity>().FromSqlRaw(CreateSqlWithParameters(sql, parameters), parameters);
//    //}
//    //public List<T> ExecSQL<T>(string query, params object[] parameters)
//    //{
//    //    var commandText = CreateSqlWithParameters(query,parameters);
//    //    using (var command = Database.GetDbConnection().CreateCommand())
//    //    {
//    //        command.CommandText = commandText;
//    //        command.CommandType = CommandType.StoredProcedure;
//    //        Database.OpenConnection();
//    //        List<T> list = new List<T>();
//    //        using (var result = command.ExecuteReader())
//    //        {
//    //            T obj = default(T);
//    //            while (result.Read())
//    //            {
//    //                obj = Activator.CreateInstance<T>();
//    //                foreach (PropertyInfo prop in obj.GetType().GetProperties())
//    //                {
//    //                    if (!object.Equals(result[prop.Name], DBNull.Value))
//    //                    {
//    //                        prop.SetValue(obj, result[prop.Name], null);
//    //                    }
//    //                }
//    //                list.Add(obj);
//    //            }
//    //        }
//    //        Database.CloseConnection();
//    //        return list;
//    //    }
//    //}

//}
