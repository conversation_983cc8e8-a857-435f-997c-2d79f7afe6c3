﻿// This file is generated by t4 generator - SystemSettings.tt
namespace Bigpara.Configuration.AdoNet
{
	public static partial class SystemSettings
    {
		public static AdUrl AdUrl = new AdUrl();
        public static ElasticsearchClientConfiguration ElasticsearchClientConfiguration = new ElasticsearchClientConfiguration();
        public static ExternalDataConfiguration ExternalDataConfiguration = new ExternalDataConfiguration();
        public static FileConfiguration FileConfiguration = new FileConfiguration();
        public static FonMarketConfiguration FonMarketConfiguration = new FonMarketConfiguration();
        public static HurPassApiConfiguration HurPassApiConfiguration = new HurPassApiConfiguration();
        public static LoggerConfiguration LoggerConfiguration = new LoggerConfiguration();
        public static MailServerConfiguration MailServerConfiguration = new MailServerConfiguration();
        public static PaymentConfiguration PaymentConfiguration = new PaymentConfiguration();
        public static RedisClientConfiguration RedisClientConfiguration = new RedisClientConfiguration();
        public static RestApiConfiguration RestApiConfiguration = new RestApiConfiguration();
        public static SeoConfiguration SeoConfiguration = new SeoConfiguration();
        public static WebSiteConfiguration WebSiteConfiguration = new WebSiteConfiguration();
        public static FirebaseConfiguration FirebaseConfiguration = new FirebaseConfiguration();	
	}

	#region " Owner Class Definations "
			
    public class AdUrl
    {
        private readonly string _ownerName = "AdUrl";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String AtaonlineAltinBannerUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "AtaonlineAltinBannerUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String AtaonlineDovizBannerUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "AtaonlineDovizBannerUrl");
            }
        }
	}	
	
    public class BigparaJobs
    {
        private readonly string _ownerName = "BigparaJobs";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String VideoCommentApiUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "VideoCommentApiUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String NoImageId
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "NoImageId");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String ImageServerUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "ImageServerUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String VideoEmbedUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "VideoEmbedUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String VideoWebUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "VideoWebUrl");
            }
        }
	}	
	
    public class BultenConfiguration
    {
        private readonly string _ownerName = "BultenConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String UnsubscribeLink
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "UnsubscribeLink");
            }
        }
	}		
	
    public class ElasticsearchClientConfiguration
    {
        private readonly string _ownerName = "ElasticsearchClientConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String Url
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "Url");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String Index
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "Index");
            }
        }
	}	
	
    public class ExternalDataConfiguration
    {
        private readonly string _ownerName = "ExternalDataConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String RadikalHaberUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "RadikalHaberUrl");
            }
        }
	}	
	
    public class FileConfiguration
    {
        private readonly string _ownerName = "FileConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String ImageUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "ImageUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String FileUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "FileUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String ImagePath
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "ImagePath");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String FilePath
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "FilePath");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String BitCoinAPIUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "BitCoinAPIUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String BitCoinAPIUrlBTCTurk
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "BitCoinAPIUrlBTCTurk");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String OranSektorelFileName
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "OranSektorelFileName");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String PiyasaTakvimiMatriksXmlUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "PiyasaTakvimiMatriksXmlUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String VideoMetaDataDownloadURL
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "VideoMetaDataDownloadURL");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String AbusiveWordsFileName
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "AbusiveWordsFileName");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String SiteMapFilePath
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "SiteMapFilePath");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String OptimizedImageUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "OptimizedImageUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String BitCoinAPIUrlBitfinex
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "BitCoinAPIUrlBitfinex");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String VideoMetaDataDownloadVarantURL
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "VideoMetaDataDownloadVarantURL");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String ImageOldUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "ImageOldUrl");
            }
        }
	}	
	
    public class FirebaseConfiguration
    {
        private readonly string _ownerName = "FirebaseConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String private_key_id
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "private_key_id");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String client_email
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "client_email");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String client_id
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "client_id");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String project_id
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "project_id");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String private_key_part
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "private_key_part");
            }
        }
	}	
	
    public class FonMarketConfiguration
    {
        private readonly string _ownerName = "FonMarketConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String Password
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "Password");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String UserName
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "UserName");
            }
        }
	}	
	
    public class HurPassApiConfiguration
    {
        private readonly string _ownerName = "HurPassApiConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String appkey
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "appkey");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String secret
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "secret");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String domain
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "domain");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.Boolean is_mobile
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.Boolean>(_ownerName, "is_mobile");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.Boolean is_native
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.Boolean>(_ownerName, "is_native");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String secret2
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "secret2");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String UserServiceUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "UserServiceUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String CallbackUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "CallbackUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String SessionServiceUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "SessionServiceUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String CookieName
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "CookieName");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String CookieSessionValueName
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "CookieSessionValueName");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String SsoSaveQueryUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "SsoSaveQueryUrl");
            }
        }
	}	
	
    public class LoggerConfiguration
    {
        private readonly string _ownerName = "LoggerConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String Level
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "Level");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String ElasticSearchConnectionString
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "ElasticSearchConnectionString");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.Boolean WatchTimerIsActive
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.Boolean>(_ownerName, "WatchTimerIsActive");
            }
        }
	}	
	
    public class MailServerConfiguration
    {
        private readonly string _ownerName = "MailServerConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.Boolean IsActive
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.Boolean>(_ownerName, "IsActive");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String MailServer
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "MailServer");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String To
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "To");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String From
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "From");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String DestekBigpara
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "DestekBigpara");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String GroupEmailBigpara
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "GroupEmailBigpara");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String Sysmon
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "Sysmon");
            }
        }
	}	
	
    public class PaymentConfiguration
    {
        private readonly string _ownerName = "PaymentConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String MembershipPath
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "MembershipPath");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String PaymentResultUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "PaymentResultUrl");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String SubscriptionAppKey
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "SubscriptionAppKey");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String SubscriptionSecretKey
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "SubscriptionSecretKey");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String SubscriptionSecret2Key
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "SubscriptionSecret2Key");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String SubscriptionDomain
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "SubscriptionDomain");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String SubscriptionDomainUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "SubscriptionDomainUrl");
            }
        }
	}	
	
    public class RedisClientConfiguration
    {
        private readonly string _ownerName = "RedisClientConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String MasterIp
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "MasterIp");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String ServiceStackKey
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "ServiceStackKey");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String ServerIP
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "ServerIP");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String Password
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "Password");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String SignalRQueueName
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "SignalRQueueName");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String SlaveIpList
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "SlaveIpList");
            }
        }
	}	
	
    public class RestApiConfiguration
    {
        private readonly string _ownerName = "RestApiConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String ApiUrl
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "ApiUrl");
            }
        }
	}	
	
    public class SeoConfiguration
    {
        private readonly string _ownerName = "SeoConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.Boolean AllowUnicodeCharsInUrls
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.Boolean>(_ownerName, "AllowUnicodeCharsInUrls");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.Boolean ConvertNonWesternChars
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.Boolean>(_ownerName, "ConvertNonWesternChars");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String ReservedUrlRecordSlugs
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "ReservedUrlRecordSlugs");
            }
        }
	}	
	
    public class WebSiteConfiguration
    {
        private readonly string _ownerName = "WebSiteConfiguration";
		
		/// <summary>
		/// 
		/// </summary>
		public System.String TestDomain
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "TestDomain");
            }
        }
		
		/// <summary>
		/// 
		/// </summary>
		public System.String LocalDomain
        {
            get
            {
                return SystemSettings.GetSystemSettingsValueByName<System.String>(_ownerName, "LocalDomain");
            }
        }
	}	
	#endregion
}