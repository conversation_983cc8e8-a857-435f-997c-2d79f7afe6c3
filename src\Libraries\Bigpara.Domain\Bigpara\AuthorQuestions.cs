namespace Bigpara.Domain.Bigpara;

public partial class AuthorQuestions
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Title { get; set; }
    public string Question { get; set; }
    public string Answer { get; set; }
    public int AnswerId { get; set; }
    public int AuthorId { get; set; }
    public string AuthorFirstName { get; set; }
    public string AuthorLastName { get; set; }
    public int CategoryId { get; set; }
    public string CategoryName { get; set; }
    public string Followers { get; set; }
    public DateTime CreatedDate { get; set; }
    public bool IsActive { get; set; }

    public string UserFullName
    {
        get
        {
            return FirstName + " " + LastName;
        }
    }

    public string AuthorFullName
    {
        get
        {
            return AuthorFirstName + " " + AuthorLastName;
        }
    }

}
