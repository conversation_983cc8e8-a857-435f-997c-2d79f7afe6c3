﻿using StackExchange.Redis;

namespace Bigpara.Service.Hangfire.Infrastructre.Publishers
{


    public class RedisPublisher : IPublisher
    {
        private readonly IConnectionMultiplexer _redis;
        private readonly ILogger<RedisPublisher> _logger;
        private readonly ISubscriber _subscriber;

        public RedisPublisher(
            IConnectionMultiplexer redis,
            ILogger<RedisPublisher> logger)
        {
            _redis = redis;
            _logger = logger;
            _subscriber = _redis.GetSubscriber();
        }

        public async Task PublishSymbolDataAsync(string symbol, Dictionary<string,string> data)
        {
            try
            {
                var channel = $"Bigpara.Service.Realtime.Hubs.TradeHub:group:symbol_chart:{symbol.ToUpper()}";
                var message = System.Text.Json.JsonSerializer.Serialize(data);
                await _subscriber.PublishAsync(channel, message);

                _logger.LogDebug($"Published symbol data for {symbol}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error publishing symbol data for {symbol}");
            }
        }

        public async Task PublishSymbolDetailAsync(string symbol, Dictionary<string,string> detail)
        {
            try
            {
                var channel = $"financial_data:symbol_detail:{symbol}";
                var message = System.Text.Json.JsonSerializer.Serialize(detail);
                await _subscriber.PublishAsync(channel, message);

                _logger.LogDebug($"Published symbol detail for {symbol}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error publishing symbol detail for {symbol}");
            }
        }

        public async Task PublishAllSymbolsAsync(List<Dictionary<string,string>> symbols)
        {
            try
            {
                var channel = "financial_data:symbol_table_all";
                var message = System.Text.Json.JsonSerializer.Serialize(symbols);
                await _subscriber.PublishAsync(channel, message);

                _logger.LogDebug($"Published all symbols data ({symbols.Count} symbols)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing all symbols data");
            }
        }

        public async Task PublishSymbolsByTypeAsync(string type, List<Dictionary<string,string>> symbols)
        {
            try
            {
                var channel = $"financial_data:symbol_table_{type}_all";
                var message = System.Text.Json.JsonSerializer.Serialize(symbols);
                await _subscriber.PublishAsync(channel, message);

                _logger.LogDebug($"Published {type} symbols data ({symbols.Count} symbols)");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error publishing {type} symbols data");
            }
        }
    }
}
