using Bigpara.Domain.Enums;

namespace Bigpara.Domain.SummaryTypes;

public partial class NewsSummaryItem
{
    public int Id { get; set; }
    public string Title { get; set; }
    public string Picture { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int ReadCount { get; set; }
    public string NewsCategoryName { get; set; }
    public NewsCategoryEnum CategoryId { get; set; }
    public string CategoryName { get; set; }
    public int NewsCategoryId { get; set; }
    public string AdvertNewUrl { get; set; }
    public int CommentCount { get; set; }
    public string SpotTitle { get; set; }
    public string SiteUrl { get; set; }
}