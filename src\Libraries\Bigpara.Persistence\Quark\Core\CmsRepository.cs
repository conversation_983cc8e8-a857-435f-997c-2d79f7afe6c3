﻿using Bigpara.Core.Application.Contracts.Quark.Core;
using Bigpara.Persistence.MongoDbContext;
using MongoDB.Driver;
using Quark.Models.Entities;
using Quark.Models.Entities.Containers;
using Quark.Models.Entities.Contents;
using Quark.Models.Entities.Properties;

namespace Bigpara.Persistence.Quark.Core;

public class CmsRepository : ICmsRepository
{
    private readonly IMongoDbContext _mongoDbContext;

    public CmsRepository(IMongoDbContext mongoDbContext)
    {
        _mongoDbContext = mongoDbContext;
    }

    public async Task<long> GetNextIncrementIdAsync(string collectionName, string application)
    {
        return await _mongoDbContext.GetNextIncrementIdAsync(collectionName, application);
    }

    public async Task<List<Folder>> GetFoldersAsync(string collectionName)
    {
        var filter = Builders<Folder>.Filter.And(
            Builders<Folder>.Filter.Eq("Application", "com.bigpara"),
            Builders<Folder>.Filter.Eq("Status", 0),
            Builders<Folder>.Filter.In("_t", ["Folder", "PersonContainer"])
        );

        var result = await _mongoDbContext.FindAsync(collectionName, filter);

        if (result == null || !result.Any())
        {
            return new List<Folder>();
        }

        return result.ToList();
    }

    public async Task<List<Property>> GetContentPropertiesAsync(string typeName)
    {
        var filter = Builders<ContentProperty>.Filter.Eq("Application", "com.bigpara");
        var result = await _mongoDbContext.FindAsync("ContentProperties", filter);

        var contentProperty = result.ToList().Find(p => p.TypeName == typeName);

        if (contentProperty != null)
        {
            foreach (var item in contentProperty.Properties)
            {
                foreach (var sitem in item.SelectValues)
                {
                    sitem.Selected = false;
                    sitem.Value = string.Empty;
                }
            }
            return contentProperty.Properties.ToList();
        }

        return new List<Property>();
    }

    public async Task InsertContentAsync(string collectionName, Article article)
    {
        await _mongoDbContext.InsertAsync(collectionName, article);
    }

    public async Task<List<Ancestor>> GetAncestorsAsync(string collectionName, string contentPath)
    {
        if (contentPath == "/")
            return new List<Ancestor>();

        var ixNameList = contentPath.Trim('/').Split('/');
        var path = "/";
        var ancestorList = new List<Ancestor>();

        var folders = await GetFoldersAsync(collectionName);

        foreach (var ixName in ixNameList)
        {
            var folder = folders?.FirstOrDefault(p => p.Path == path && p.IxName == ixName);
            if (folder == null)
            {
                continue;
            }
            ancestorList.Add(new Ancestor
            {
                Id = folder._Id,
                ContentType = folder.ContentType,
                Title = folder.Title,
                Path = folder.Path,
                IxName = folder.IxName,
                SelfPath = folder.SelfPath,
                Url = folder.Url,
                IsBreadCrumb = true
            });
            path += $"{ixName}/";
        }

        return ancestorList;
    }

    public async Task<IList<Permission>> GetPermissionsAsync(string collectionName, string contentPath)
    {
        var folders = await GetFoldersAsync(collectionName);

        if (contentPath == "/")
            return new List<Permission>();

        var ixNameList = contentPath.Trim('/').Split('/');
        foreach (var ixName in ixNameList)
        {
            var anc = folders.FirstOrDefault(p => p.Path == "/" && p.IxName == ixName);

            if (anc?.Permissions != null)
                return anc.Permissions;
        }

        return new List<Permission>();
    }

    public async Task<long> GetLastKapNewsTimestampAsync(string collectionName, string application, string path)
    {
        var filter = Builders<Article>.Filter.And(
            Builders<Article>.Filter.Eq(x => x.Application, application),
            Builders<Article>.Filter.Eq(x => x.ContentType, "Article"),
            Builders<Article>.Filter.Eq(x => x.Path, path)
        );

        var sort = Builders<Article>.Sort.Descending(x => x.CreatedDate);

        var latest = await _mongoDbContext
            .GetCollection<Article>(collectionName)
            .Find(filter)
            .Sort(sort)
            .Limit(1)
            .FirstOrDefaultAsync();

        if (latest != null)
        {
            var info = TimeZoneInfo.FindSystemTimeZoneById("Turkey Standard Time");
            var localTime = TimeZoneInfo.ConvertTime(latest.CreatedDate.Value, info);
            var timestamp = new DateTimeOffset(localTime).ToUnixTimeMilliseconds();

            return timestamp;
        }

        return -1;
    }

    public async Task<bool> ContentExistsAsync(string collectionName, string referenceId)
    {
        var filter = Builders<Article>.Filter.Eq(x => x.ReferenceId, referenceId);
        var result = await _mongoDbContext.FindOneAsync(collectionName, filter);
        return result != null;
    }
}
