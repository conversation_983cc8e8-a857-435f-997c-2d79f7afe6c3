﻿using Bigpara.Core.Configuration;

namespace Bigpara.Service.Hangfire.Services.Configuration;

public class FeedConfigurationService
{
    private readonly IConfiguration _configuration;

    public FeedConfigurationService(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public FeedConfiguration LoadFeedConfiguration()
    {
        var feedSection = _configuration.GetSection("Feed");

        var feedConfig = new FeedConfiguration
        {
            SymbolApi = feedSection.GetValue<string>("SymbolApi"),
            SektorApi = feedSection.GetValue<string>("SektorApi"),

            Items = feedSection.GetChildren()
                .Where(x => x.Key != "SymbolApi" && x.Key != "SektorA<PERSON>")
                .ToDictionary(x => x.Key, x => feedSection.GetSection(x.Key).Get<FeedItem>())
        };

        return feedConfig;
    }
}