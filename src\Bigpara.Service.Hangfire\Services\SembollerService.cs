﻿using Bigpara.Cache;
using Bigpara.Cache.Interfaces;
using Bigpara.Core.Application.Contracts.Matriks.Sembol;
using Bigpara.Domain;
using Bigpara.Domain.Matriks;
using Bigpara.Domain.Matriks.Definitions;
using Bigpara.Domain.Redis;
using Bigpara.Service.Hangfire.Services.Interfaces;
using System.Linq.Expressions;

namespace Bigpara.Service.Hangfire.Services;

public class SembollerService : ISembollerService
{
    private const string CachekeyAllSemboller = "bigpara.all.semboller";
    private const string CachekeyAllSembollerFiltered = "bigpara.semboller.filtered";
    private const string CachekeySembollerWithPiyasaId = "bigpara.semboller.with.piyasa.id-{0}";


    private readonly ISembolRepository _sembolRepository;
    private readonly ICacheService _cacheService;

    public SembollerService(ISembolRepository sembolRepository, ICacheService cacheService)
    {
        _sembolRepository = sembolRepository;
        _cacheService = cacheService;
    }

    public virtual Task<List<Semboller>> GetSembollersAsync()
    {
        return Task.FromResult(_cacheService.Get(CachekeyAllSemboller, 15, () => _sembolRepository.Query(x => x.Aktif).ToList()));
    }

    public async Task<List<T>> GetSembollersAsync<T>(Expression<Func<Semboller, bool>> predicate, Expression<Func<Semboller, T>> selector)
    {
        var result = await _cacheService.GetAsync(CachekeyAllSembollerFiltered, 15, () => Task.FromResult(_sembolRepository.Query(predicate).Select(selector).ToList()));

        return result;
    }

    public virtual Task<List<Semboller>> GetSembollersByPiyasaIdAsync(int piyasaId)
    {
        return Task.FromResult(_sembolRepository.Query(x => x.Aktif && x.PiyasaId == piyasaId).ToList());
    }

    public async Task<List<Semboller>> GrafikGunlukSembols(DateTime dateTime)
    {
        return await _sembolRepository.GetSembolsGrafikGunluks(dateTime);
    }

    public async Task<List<Endeks>> GetEndeksList()
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_MATRIKS_ENDEKS_ALL_KEY);
        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _sembolRepository.GetEndeksList())).ToList();
    }

    public async Task<List<SembolEndeks>> GetSembolEndekses(string sembol)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_MATRIKS_SEMBOL_ENDEKS_ALL_KEY_BY_SEMBOL, sembol);
        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _sembolRepository.GetSembolEndekses(sembol))).ToList();
    }
    public async Task<Semboller> GetSembollerBySembolId(int sembolId)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_MATRIKS_SEMBOL_TANIM_DETAY_KEY_BY_SEMBOLID, sembolId);
        return await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _sembolRepository.GetSembollerBySembolId(sembolId));
    }
    public async Task<KisaTanimSemboller> GetSymbolSummaryBySymbol(string sembol)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_MATRIKS_SEMBOL_KISA_TANIM_DETAY_KEY_BY_SEMBOL, sembol);
        return await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _sembolRepository.GetSymbolSummaryBySymbol(sembol));
    }
    public async Task<List<Endeks>> GetSembolEndekslerListesi()
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_MATRIKS_SEMBOL_ENDEKS_ALL_KEY);
        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _sembolRepository.GetSembolEndekslerListesi())).ToList();
    }

    public async Task<List<Semboller>> GetHisseSembollerByPiyasaIdList(int piyasaId)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_MATRIKS_SEMBOL_TANIM_DETAY_KEY_BY_PIYASAID, piyasaId);
        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _sembolRepository.GetHisseSembollerByPiyasaIdList(piyasaId))).ToList();
    }

    public async Task<List<Endeks>> GetSembolByEndeks(string endeks)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_MATRIKS_ENDEKS_ALL_KEY_BY_ENDEKS_SEMBOL, endeks);
        return (await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _sembolRepository.GetSembolByEndeks(endeks))).ToList();
    }

    public async Task<RedisReadCount> GetSymbolReadCountsAllPeriods(int symbolId)
    {

        string key = string.Format("bigpara.semboller.services.GetSymbolReadCountsAllPeriods{0}", symbolId);
        return await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _sembolRepository.GetSymbolReadCountsAllPeriods(symbolId));
    }

    public async Task<int> InsertSymbolReadCount(SymbolReadCount symbolReadCount)
    {
        return _sembolRepository.InsertSymbolReadCount(symbolReadCount).GetAwaiter().GetResult();
    }

    public async Task<SembollerEk> GetSembollerEkData(int sembolId)
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_MATRIKS_SEMBOLLEREK_BY_SEMBOLID, sembolId);
        return await _cacheService.GetAsync(key, CacheKeys.CACHE_LONG_DATA_DURATION, () => _sembolRepository.GetSembollerEkData(sembolId));
    }


    public async Task<int> UpdateSymbolReadCount(int sembolId, DateTime countDate)
    {
        return _sembolRepository.UpdateSymbolReadCount(sembolId, countDate).GetAwaiter().GetResult();
    }

    public async Task<List<SymbolCountSummary>> GetSembolReadCountSummaries()
    {
        string key = string.Format(CacheKeys.BIGPARA_BORSA_MATRIKS_SEMBOL_READ_COUNTS_SUMMARY);
        return await _cacheService.GetAsync(key, CacheKeys.CACHE_DATA_5DK_DURATION, () => _sembolRepository.GetSembolReadCountSummaries());
    }
}
