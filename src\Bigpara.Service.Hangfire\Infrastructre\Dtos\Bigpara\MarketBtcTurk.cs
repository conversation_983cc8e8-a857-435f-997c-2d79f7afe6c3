﻿using Newtonsoft.Json;

namespace Bigpara.Service.Hangfire.Infrastructre.Dtos.Bigpara;

public class MarketBtcTurk
{
    [JsonProperty("high")]
    public double? High { get; set; }

    //Bitcoincharts "Close" alanına karşılık gelmektedir.
    [JsonProperty("last")]
    public double? Last { get; set; }

    [JsonProperty("timestamp")]
    public string Timestamp { get; set; }

    [JsonProperty("bid")]
    public double? Bid { get; set; }

    [JsonProperty("volume")]
    public double? Volume { get; set; }

    [JsonProperty("low")]
    public double? Low { get; set; }

    [JsonProperty("ask")]
    public double? Ask { get; set; }
}
