﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Foreks\**" />
    <EmbeddedResource Remove="Foreks\**" />
    <None Remove="Foreks\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Elasticsearch.Net" Version="7.11.1" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.5" />
    <PackageReference Include="MongoDB.Driver" Version="2.26.0" />
    <PackageReference Include="NEST" Version="7.11.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Bigpara.Core\Bigpara.Core.csproj" />
    <ProjectReference Include="..\Bigpara.Domain\Bigpara.Domain.csproj" />
  </ItemGroup>

</Project>
