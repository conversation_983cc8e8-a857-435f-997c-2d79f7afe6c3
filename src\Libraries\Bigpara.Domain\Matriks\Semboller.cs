namespace Bigpara.Domain.Matriks;

//public partial class Semboller
//{
//    public int SEMBOLID { get; set; }
//    public string SEMBOL { get; set; }
//    public string ACIKLAMA { get; set; }
//    public byte PIYASAID { get; set; }
//    public string ENDEKS { get; set; }
//    public string IMKB<PERSON>ZARKOD { get; set; }
//    public string IMKBHISSETIP { get; set; }
//    public Nullable<byte> SEKTORID { get; set; }
//    public bool SEANSVAR { get; set; }
//    public bool DERINLIKVAR { get; set; }
//    public byte ONDALIKBASAMAK { get; set; }
//    public bool AKTIF { get; set; }
//    public string HISSEGRUBU { get; set; }
//    public string ISLEMTURU { get; set; }
//    public string BRUTTAKAS { get; set; }
//    public string ENDEKSLER { get; set; }
//    public string FOREKSKOD { get; set; }
//}
public partial class KisaTanimSemboller
{
    public int SEMBOLID { get; set; }
    public string SEMBOL { get; set; }
    public string ACIKLAMA { get; set; }
    public string SEMBOLFOREKS { get; set; }

    public int Order { get; set; }
}
