﻿using Bigpara.Core.Application.Contracts.Matriks.Borsa;
using Bigpara.Domain.Enums;
using Bigpara.Domain.SummaryTypes;
using Microsoft.Data.SqlClient;
using System.Data;

namespace Bigpara.Persistence.Matriks.Borsa;

public class GecmisKapanisRepository : IGecmisKapanisRepository
{
    private readonly IMatriksDbContext _matriksDbContext;

    public GecmisKapanisRepository
    (
        IMatriksDbContext matriksDbContext
    )
    {
        _matriksDbContext = matriksDbContext;
    }

    public List<SembolOzet> GetGecmisKapanisList(DateTime? selectedDate, MarketTypes marketType, int pageNo, byte pageSize, char? letter, out int totalCount)
    {
        if (selectedDate.Value.Year == 1)
            selectedDate = DateTime.Today;

        var outTotalCountParameter = new SqlParameter() { ParameterName = "totalCount", SqlDbType = SqlDbType.Int, Direction = ParameterDirection.Output };

        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("tarih",selectedDate),
            new SqlParameter("piyasaId",(int)marketType),
            new SqlParameter("pageNo",pageNo),
            new SqlParameter("pageSize", pageSize),
            new SqlParameter("letter",letter),
            outTotalCountParameter
        };

        var result = _matriksDbContext.ExecuteStoredProcedureAsync<SembolOzet>("bp.pGetBistKapanisDataTarihSembolPiyasaId", parameters.ToArray()).GetAwaiter().GetResult();

        totalCount = (int)(outTotalCountParameter.Value ?? 0);

        return result ?? new List<SembolOzet>();
    }

    public async Task<List<SembolOzet>> GetGecmisKapanisList(DateTime? selectedDate, MarketTypes marketType, char? letter)
    {
        if (selectedDate.Value.Year == 1)
            selectedDate = DateTime.Today;

        var parameters = new List<SqlParameter>()
        {
            new SqlParameter("tarih",selectedDate),
            new SqlParameter("piyasaId",(int)marketType),
            new SqlParameter("letter",letter),
        };

        var result = await _matriksDbContext.ExecuteStoredProcedureAsync<SembolOzet>("bp.pGetBistKapanisDataTarihPiyasaIdV2", parameters.ToArray());

        return result ?? new List<SembolOzet>();
    }

    public async Task<SembolOzet> GetGecmisKapanisMaxDate()
    {
        return await _matriksDbContext.ExecuteQuerySingleAsync<SembolOzet>("bp.pGetBistKapanisMaxDate");
    }
}
